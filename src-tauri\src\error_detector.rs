// Advanced Error Detection System for FFmpeg Streaming
// Detects all critical streaming errors that cause connection failures

use std::collections::HashMap;
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum StreamError {
    // Network Errors
    NetworkReset,           // WSAECONNRESET (-10054)
    NetworkAborted,         // WSAECONNABORTED (-10053)
    ConnectionRefused,      // Connection refused
    ConnectionTimeout,      // Connection timeout
    
    // Muxer Errors
    MuxerFailure,          // Error muxing packet
    HeaderUpdateFailed,    // Failed to update header
    TrailerWriteFailed,    // Error writing trailer
    UnexpectedEOF,         // End of file errors
    
    // Input/Output Errors
    FileNotFound,          // Input file issues
    PermissionDenied,      // Permission issues
    InvalidArgument,       // Invalid FFmpeg arguments
    
    // Resource Errors
    MemoryExhaustion,      // Memory/resource issues
    BufferOverflow,        // Buffer overflow
    
    // RTMP Specific
    RTMPHandshakeFailed,   // RTMP handshake issues
    RTMPAuthFailed,        // RTMP authentication failed
    RTMPServerError,       // RTMP server errors
    
    // Encoding Errors
    EncodingFailed,        // Video/audio encoding issues
    BitrateError,          // Bitrate related issues
}

impl StreamError {
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            StreamError::NetworkReset | StreamError::NetworkAborted => ErrorSeverity::Critical,
            StreamError::MuxerFailure | StreamError::HeaderUpdateFailed => ErrorSeverity::Critical,
            StreamError::UnexpectedEOF | StreamError::TrailerWriteFailed => ErrorSeverity::Critical,
            StreamError::MemoryExhaustion | StreamError::BufferOverflow => ErrorSeverity::Critical,
            StreamError::RTMPHandshakeFailed | StreamError::RTMPAuthFailed => ErrorSeverity::High,
            StreamError::ConnectionRefused | StreamError::ConnectionTimeout => ErrorSeverity::High,
            StreamError::FileNotFound | StreamError::PermissionDenied => ErrorSeverity::Medium,
            StreamError::InvalidArgument | StreamError::EncodingFailed => ErrorSeverity::Medium,
            StreamError::RTMPServerError | StreamError::BitrateError => ErrorSeverity::Low,
        }
    }
    
    pub fn is_recoverable(&self) -> bool {
        match self {
            StreamError::NetworkReset | StreamError::NetworkAborted => true,
            StreamError::ConnectionRefused | StreamError::ConnectionTimeout => true,
            StreamError::RTMPHandshakeFailed | StreamError::RTMPServerError => true,
            StreamError::MuxerFailure | StreamError::UnexpectedEOF => true,
            StreamError::MemoryExhaustion | StreamError::BufferOverflow => true,
            StreamError::FileNotFound | StreamError::PermissionDenied => false,
            StreamError::InvalidArgument => false,
            StreamError::RTMPAuthFailed => false,
            StreamError::HeaderUpdateFailed | StreamError::TrailerWriteFailed => true,
            StreamError::EncodingFailed | StreamError::BitrateError => true,
        }
    }
    
    pub fn suggested_action(&self) -> RecoveryAction {
        match self {
            StreamError::NetworkReset | StreamError::NetworkAborted => RecoveryAction::ReduceBitrateAndReconnect,
            StreamError::MuxerFailure | StreamError::UnexpectedEOF => RecoveryAction::RestartWithStableSettings,
            StreamError::MemoryExhaustion | StreamError::BufferOverflow => RecoveryAction::RestartWithEmergencySettings,
            StreamError::ConnectionRefused | StreamError::ConnectionTimeout => RecoveryAction::RetryWithDelay,
            StreamError::RTMPHandshakeFailed | StreamError::RTMPServerError => RecoveryAction::RetryWithDelay,
            StreamError::HeaderUpdateFailed | StreamError::TrailerWriteFailed => RecoveryAction::RestartWithStableSettings,
            StreamError::EncodingFailed | StreamError::BitrateError => RecoveryAction::ReduceBitrateAndReconnect,
            _ => RecoveryAction::Stop,
        }
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ErrorSeverity {
    Critical,   // Immediate action required
    High,       // Action required soon
    Medium,     // Monitor and potentially act
    Low,        // Log and continue
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum RecoveryAction {
    RetryWithDelay,                 // Retry with exponential backoff
    ReduceBitrateAndReconnect,      // Lower bitrate and reconnect
    RestartWithStableSettings,      // Restart with stable profile
    RestartWithEmergencySettings,   // Restart with emergency profile
    Stop,                          // Stop streaming, manual intervention needed
}

pub struct ErrorDetector {
    error_patterns: HashMap<String, StreamError>,
    error_history: Vec<(DateTime<Utc>, StreamError, String)>,
    consecutive_errors: HashMap<StreamError, u32>,
}

impl ErrorDetector {
    pub fn new() -> Self {
        let mut error_patterns = HashMap::new();
        
        // Network Errors
        error_patterns.insert("-10054".to_string(), StreamError::NetworkReset);
        error_patterns.insert("WSAECONNRESET".to_string(), StreamError::NetworkReset);
        error_patterns.insert("Connection reset by peer".to_string(), StreamError::NetworkReset);
        
        error_patterns.insert("-10053".to_string(), StreamError::NetworkAborted);
        error_patterns.insert("WSAECONNABORTED".to_string(), StreamError::NetworkAborted);
        error_patterns.insert("Software caused connection abort".to_string(), StreamError::NetworkAborted);
        
        error_patterns.insert("Connection refused".to_string(), StreamError::ConnectionRefused);
        error_patterns.insert("Failed to connect".to_string(), StreamError::ConnectionRefused);
        error_patterns.insert("Connection timed out".to_string(), StreamError::ConnectionTimeout);
        error_patterns.insert("Timeout".to_string(), StreamError::ConnectionTimeout);
        
        // Muxer Errors
        error_patterns.insert("Error muxing a packet".to_string(), StreamError::MuxerFailure);
        error_patterns.insert("Error submitting a packet to the muxer".to_string(), StreamError::MuxerFailure);
        error_patterns.insert("Failed to update header with correct duration".to_string(), StreamError::HeaderUpdateFailed);
        error_patterns.insert("Failed to update header with correct filesize".to_string(), StreamError::HeaderUpdateFailed);
        error_patterns.insert("Error writing trailer".to_string(), StreamError::TrailerWriteFailed);
        error_patterns.insert("Error closing file".to_string(), StreamError::TrailerWriteFailed);
        
        // EOF Errors
        error_patterns.insert("End of file".to_string(), StreamError::UnexpectedEOF);
        
        // Input/Output Errors
        error_patterns.insert("No such file".to_string(), StreamError::FileNotFound);
        error_patterns.insert("Error opening input files".to_string(), StreamError::FileNotFound);
        error_patterns.insert("Permission denied".to_string(), StreamError::PermissionDenied);
        error_patterns.insert("Invalid argument".to_string(), StreamError::InvalidArgument);
        
        // RTMP Errors
        error_patterns.insert("RTMP_Connect0".to_string(), StreamError::RTMPHandshakeFailed);
        error_patterns.insert("RTMP_Connect1".to_string(), StreamError::RTMPHandshakeFailed);
        error_patterns.insert("Failed to connect to RTMP server".to_string(), StreamError::RTMPHandshakeFailed);
        error_patterns.insert("RTMP server sent error".to_string(), StreamError::RTMPServerError);
        error_patterns.insert("Authentication failed".to_string(), StreamError::RTMPAuthFailed);
        
        // Resource Errors
        error_patterns.insert("Cannot allocate memory".to_string(), StreamError::MemoryExhaustion);
        error_patterns.insert("Out of memory".to_string(), StreamError::MemoryExhaustion);
        error_patterns.insert("Too many packets buffered".to_string(), StreamError::BufferOverflow);
        error_patterns.insert("Queue input is backward in time".to_string(), StreamError::BufferOverflow);
        
        // Encoding Errors
        error_patterns.insert("Encoding failed".to_string(), StreamError::EncodingFailed);
        error_patterns.insert("Error while encoding".to_string(), StreamError::EncodingFailed);
        error_patterns.insert("Bitrate too high".to_string(), StreamError::BitrateError);
        error_patterns.insert("Buffer underrun".to_string(), StreamError::BitrateError);
        
        Self {
            error_patterns,
            error_history: Vec::new(),
            consecutive_errors: HashMap::new(),
        }
    }
    
    pub fn detect_error(&mut self, log_line: &str) -> Option<StreamError> {
        let line_lower = log_line.to_lowercase();
        
        // Check each pattern
        for (pattern, error_type) in &self.error_patterns {
            if line_lower.contains(&pattern.to_lowercase()) {
                let now = Utc::now();
                
                // Update consecutive error count
                let count = self.consecutive_errors.entry(error_type.clone()).or_insert(0);
                *count += 1;
                
                // Add to history
                self.error_history.push((now, error_type.clone(), log_line.to_string()));
                
                // Keep only last 100 errors
                if self.error_history.len() > 100 {
                    self.error_history.drain(0..50);
                }
                
                log::error!("🚨 Detected {} error: {}", 
                           format!("{:?}", error_type), log_line);
                
                return Some(error_type.clone());
            }
        }
        
        None
    }
    
    pub fn is_critical_error(&self, error: &StreamError) -> bool {
        error.severity() == ErrorSeverity::Critical
    }
    
    pub fn get_consecutive_count(&self, error: &StreamError) -> u32 {
        self.consecutive_errors.get(error).copied().unwrap_or(0)
    }
    
    pub fn reset_consecutive_count(&mut self, error: &StreamError) {
        self.consecutive_errors.insert(error.clone(), 0);
    }
    
    pub fn get_recent_errors(&self, minutes: i64) -> Vec<&(DateTime<Utc>, StreamError, String)> {
        let cutoff = Utc::now() - chrono::Duration::minutes(minutes);
        self.error_history.iter()
            .filter(|(timestamp, _, _)| *timestamp > cutoff)
            .collect()
    }
    
    pub fn get_error_summary(&self) -> HashMap<StreamError, u32> {
        let mut summary = HashMap::new();
        for (_, error, _) in &self.error_history {
            let count = summary.entry(error.clone()).or_insert(0);
            *count += 1;
        }
        summary
    }
    
    pub fn should_trigger_recovery(&self, error: &StreamError) -> bool {
        let consecutive = self.get_consecutive_count(error);
        let recent_errors = self.get_recent_errors(5); // Last 5 minutes
        
        match error.severity() {
            ErrorSeverity::Critical => consecutive >= 1, // Immediate action
            ErrorSeverity::High => consecutive >= 2,     // After 2 consecutive
            ErrorSeverity::Medium => consecutive >= 3,   // After 3 consecutive
            ErrorSeverity::Low => recent_errors.len() > 5, // Too many in short time
        }
    }
}

impl Default for ErrorDetector {
    fn default() -> Self {
        Self::new()
    }
}
