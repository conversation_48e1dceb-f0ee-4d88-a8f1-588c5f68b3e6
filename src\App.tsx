import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import './App.css';
import LicenseModal from "./components/LicenseModal";
import Dashboard from './components/Dashboard';

interface LicenseStatus {
  is_valid: boolean;
  message: string;
}

function App() {
  const [isLicenseValid, setIsLicenseValid] = useState<boolean | null>(null);
  const [showLicenseModal, setShowLicenseModal] = useState(false);

  useEffect(() => {
    console.log("App useEffect running...");
    // Check if license was previously validated
    const savedLicense = localStorage.getItem('license_validated');
    console.log("Saved license:", savedLicense);

    if (savedLicense === 'true') {
      setIsLicenseValid(true);
      setShowLicenseModal(false);
    } else {
      setIsLicenseValid(false);
      setShowLicenseModal(true);
    }
  }, []);

  const handleLicenseSubmit = async (licenseKey: string) => {
    try {
      console.log("Checking license:", licenseKey);

      // TEMPORARY WORKAROUND: Since Tauri backend can't build due to Windows linker issues,
      // we'll simulate the license check locally for development testing
      const VALID_LICENSE = "PACE-2025-STREAMING";

      let result: LicenseStatus;
      if (licenseKey.trim() === VALID_LICENSE) {
        result = {
          is_valid: true,
          message: "License hợp lệ"
        };
      } else {
        result = {
          is_valid: false,
          message: "License không hợp lệ. Vui lòng nhập mã license đúng."
        };
      }

      // Try to call Tauri backend, but fallback to local validation if it fails
      try {
        const backendResult: LicenseStatus = await invoke("check_license", { licenseKey });
        result = backendResult;
        console.log("Using backend license result:", result);
      } catch (backendError) {
        console.warn("Backend not available, using local license validation:", backendError);
        console.log("Using local license result:", result);
      }

      if (result.is_valid) {
        setIsLicenseValid(true);
        setShowLicenseModal(false);
        localStorage.setItem('license_validated', 'true');
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error("License validation error:", error);
      alert("Có lỗi xảy ra khi kiểm tra license");
    }
  };

  console.log("App render - isLicenseValid:", isLicenseValid, "showLicenseModal:", showLicenseModal);

  // Show loading only if we haven't determined license status yet
  if (isLicenseValid === null) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang khởi tạo ứng dụng...</p>
        </div>
      </div>
    );
  }

  // License modal with HeadlessUI
  if (showLicenseModal && !isLicenseValid) {
    return <LicenseModal onSubmit={handleLicenseSubmit} />;
  }

  // Full Dashboard with Stream Management
  if (isLicenseValid) {
    return <Dashboard />;
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <p className="text-gray-600">Trạng thái không xác định</p>
      </div>
    </div>
  );
}

export default App;
