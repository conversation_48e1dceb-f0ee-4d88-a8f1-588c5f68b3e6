mod models;
mod stream_manager;
mod license;
mod simple_ffmpeg;
mod long_stream_optimizer;
mod ffmpeg_command_builder;
mod error_detector;
mod reconnection_manager;

use std::sync::Arc;
use std::path::PathBuf;
use tauri::{<PERSON>, App<PERSON><PERSON><PERSON>, Manager, Emitter};
use uuid::Uuid;

use models::{CreateStreamRequest, StreamInfo, AppSettings, StreamStatus};
use stream_manager::StreamManager;
use license::check_license;
use simple_ffmpeg::{SimpleFFmpeg, SimpleStreamConfig};
use long_stream_optimizer::{LongStreamOptimizer, NetworkQuality, detect_network_quality};
use serde::{Serialize, Deserialize};

// Helper function to load app settings (shared across modules)
fn load_app_settings() -> Result<AppSettings, Box<dyn std::error::Error>> {
    let data_dir = dirs::data_dir()
        .ok_or("Không thể tìm thư mục data")?
        .join("youtube-streaming");

    let settings_file = data_dir.join("settings.json");

    if !settings_file.exists() {
        return Ok(AppSettings::default());
    }

    let json = std::fs::read_to_string(settings_file)?;
    let settings: AppSettings = serde_json::from_str(&json)?;
    Ok(settings)
}

type StreamManagerState = Arc<StreamManager>;

// Function to get bundled FFmpeg path
fn get_ffmpeg_path(app_handle: &AppHandle) -> Result<PathBuf, String> {
    let resource_path = if cfg!(target_os = "windows") {
        "binaries/ffmpeg.exe"
    } else {
        "binaries/ffmpeg"
    };

    app_handle
        .path()
        .resolve(resource_path, tauri::path::BaseDirectory::Resource)
        .map_err(|e| format!("Failed to resolve FFmpeg resource: {}", e))
}

#[tauri::command]
async fn get_bundled_ffmpeg_path(app_handle: AppHandle) -> Result<String, String> {
    let path = get_ffmpeg_path(&app_handle)?;
    Ok(path.to_string_lossy().to_string())
}

#[tauri::command]
async fn create_stream(
    manager: State<'_, StreamManagerState>,
    request: CreateStreamRequest,
) -> Result<Uuid, String> {
    // Parse and validate datetime fields first
    let parsed_start_time = request.parse_start_time().map_err(|e| format!("Start time error: {}", e))?;
    let parsed_end_time = request.parse_end_time().map_err(|e| format!("End time error: {}", e))?;

    // Validate scheduled stream logic
    if !request.start_immediately {
        if parsed_start_time.is_none() {
            return Err("Scheduled stream phải có start_time".to_string());
        }

        let start_time = parsed_start_time.unwrap();
        let now = chrono::Utc::now();

        if start_time <= now {
            return Err("Thời gian bắt đầu phải trong tương lai".to_string());
        }

        log::info!("📅 Creating scheduled stream '{}' to start at {}",
            request.name, start_time.format("%Y-%m-%d %H:%M:%S UTC"));
    }

    // Validate end time logic
    if let Some(end_time) = parsed_end_time {
        let start_time = if request.start_immediately {
            chrono::Utc::now()
        } else {
            parsed_start_time.unwrap()
        };

        if end_time <= start_time {
            return Err("Thời gian kết thúc phải sau thời gian bắt đầu".to_string());
        }
    }

    let config = models::StreamConfig::new(request.clone()).map_err(|e| e.to_string())?;
    let id = config.id;

    manager.add_stream(config).map_err(|e| e.to_string())?;

    // Don't auto-start here, let frontend handle it
    // This prevents conflicts and gives better control

    Ok(id)
}

#[tauri::command]
async fn get_all_streams(manager: State<'_, StreamManagerState>) -> Result<Vec<StreamInfo>, String> {
    Ok(manager.get_all_streams())
}

#[tauri::command]
async fn get_stream(manager: State<'_, StreamManagerState>, id: String) -> Result<Option<StreamInfo>, String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    Ok(manager.get_stream(uuid))
}

#[tauri::command]
async fn start_stream(manager: State<'_, StreamManagerState>, id: String) -> Result<(), String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    manager.start_stream(uuid).map_err(|e| e.to_string())
}

#[tauri::command]
async fn stop_stream(manager: State<'_, StreamManagerState>, id: String) -> Result<(), String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    manager.stop_stream(uuid).map_err(|e| e.to_string())
}

#[tauri::command]
async fn restart_stream(
    manager: State<'_, StreamManagerState>,
    id: String,
) -> Result<(), String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;

    // Stop the stream first
    if let Err(e) = manager.stop_stream(uuid) {
        log::warn!("Error stopping stream before restart: {}", e);
    }

    // Wait a moment for cleanup
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

    // Start the stream again
    manager.start_stream(uuid).map_err(|e| e.to_string())
}

#[tauri::command]
async fn delete_stream(manager: State<'_, StreamManagerState>, id: String) -> Result<(), String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    manager.delete_stream(uuid).map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_stream_stats(manager: State<'_, StreamManagerState>, id: String) -> Result<Option<models::StreamStats>, String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    Ok(manager.get_stream_stats(uuid))
}

#[tauri::command]
async fn update_stream(
    manager: State<'_, StreamManagerState>,
    id: String,
    name: String,
    input_file: String,
    youtube_event_code: String,
    notes: String
) -> Result<(), String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    let rtmp_url = format!("rtmp://a.rtmp.youtube.com/live2/{}", youtube_event_code);
    let notes_opt = if notes.trim().is_empty() { None } else { Some(notes) };

    manager.update_stream(uuid, name, input_file, rtmp_url, notes_opt)
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_stream_logs(manager: State<'_, StreamManagerState>, id: String) -> Result<Vec<String>, String> {
    let uuid = Uuid::parse_str(&id).map_err(|e| e.to_string())?;
    Ok(manager.get_stream_logs(uuid))
}

#[tauri::command]
async fn check_running_streams(manager: State<'_, StreamManagerState>) -> Result<Vec<StreamInfo>, String> {
    let all_streams = manager.get_all_streams();
    let running_streams: Vec<StreamInfo> = all_streams
        .into_iter()
        .filter(|stream| stream.status == StreamStatus::Running)
        .collect();
    Ok(running_streams)
}

#[tauri::command]
async fn stop_all_streams(manager: State<'_, StreamManagerState>) -> Result<u32, String> {
    let all_streams = manager.get_all_streams();
    let mut stopped_count = 0u32;

    for stream in all_streams {
        if stream.status == StreamStatus::Running {
            let uuid = stream.id;
            if manager.stop_stream(uuid).is_ok() {
                stopped_count += 1;
                log::info!("✅ Stopped stream '{}' during app shutdown", stream.name);
            }
        }
    }

    log::info!("🛑 Stopped {} streams during app shutdown", stopped_count);
    Ok(stopped_count)
}

#[tauri::command]
async fn force_close_app(manager: State<'_, StreamManagerState>) -> Result<(), String> {
    log::info!("🚪 Force closing app, stopping all streams...");

    // Stop all running streams
    let _ = stop_all_streams(manager).await;

    // Give a moment for cleanup
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    log::info!("👋 App shutdown complete");
    std::process::exit(0);
}



#[tauri::command]
async fn get_stream_error_details(manager: State<'_, StreamManagerState>, stream_id: String) -> Result<Vec<String>, String> {
    let id = Uuid::parse_str(&stream_id).map_err(|e| e.to_string())?;
    let logs = manager.get_stream_logs(id);

    // Filter for error-related logs
    let error_logs: Vec<String> = logs.into_iter()
        .filter(|log| {
            log.contains("❌") || log.contains("Error") || log.contains("error") ||
            log.contains("Failed") || log.contains("failed") || log.contains("⚠️") ||
            log.contains("Connection refused") || log.contains("timed out")
        })
        .collect();

    Ok(error_logs)
}

#[tauri::command]
async fn test_rtmp_connection(app_handle: AppHandle, rtmp_url: String) -> Result<String, String> {
    log::info!("🧪 Testing RTMP connection to: {}", rtmp_url);

    // Validate RTMP URL format
    if !rtmp_url.starts_with("rtmp://") && !rtmp_url.starts_with("rtmps://") {
        return Err("RTMP URL không hợp lệ. Phải bắt đầu với rtmp:// hoặc rtmps://".to_string());
    }

    let ffmpeg_path = match get_ffmpeg_path(&app_handle) {
        Ok(path) => path,
        Err(_) => return Err("FFmpeg không khả dụng".to_string()),
    };

    // Test connection with a short dummy stream
    let mut cmd = std::process::Command::new(&ffmpeg_path);
    cmd.args([
        "-f", "lavfi",
        "-i", "testsrc2=duration=5:size=320x240:rate=30",
        "-f", "lavfi",
        "-i", "sine=frequency=1000:duration=5",
        "-c:v", "libx264",
        "-preset", "ultrafast",
        "-b:v", "500k",
        "-c:a", "aac",
        "-b:a", "64k",
        "-f", "flv",
        "-t", "5",
        &rtmp_url
    ]);

    match cmd.output() {
        Ok(output) => {
            let stderr = String::from_utf8_lossy(&output.stderr);
            if stderr.contains("Server bandwidth") || stderr.contains("Metadata:") {
                Ok("✅ RTMP connection successful".to_string())
            } else if stderr.contains("Connection refused") || stderr.contains("Failed to connect") {
                Err(format!("❌ RTMP connection failed: {}", stderr))
            } else {
                Ok(format!("⚠️ Connection test completed. Output: {}", stderr))
            }
        }
        Err(e) => Err(format!("❌ Failed to run connection test: {}", e))
    }
}

// Settings commands
#[tauri::command]
async fn get_settings() -> Result<AppSettings, String> {
    let data_dir = dirs::data_dir()
        .ok_or_else(|| "Không thể tìm thư mục data".to_string())?
        .join("youtube-streaming");

    std::fs::create_dir_all(&data_dir).map_err(|e| e.to_string())?;
    let settings_file = data_dir.join("settings.json");

    if !settings_file.exists() {
        let default_settings = AppSettings::default();
        let json = serde_json::to_string_pretty(&default_settings).map_err(|e| e.to_string())?;
        std::fs::write(settings_file, json).map_err(|e| e.to_string())?;
        return Ok(default_settings);
    }

    let json = std::fs::read_to_string(settings_file).map_err(|e| e.to_string())?;
    let settings: AppSettings = serde_json::from_str(&json).map_err(|e| e.to_string())?;
    Ok(settings)
}

#[tauri::command]
async fn save_settings(settings: AppSettings) -> Result<(), String> {
    let data_dir = dirs::data_dir()
        .ok_or_else(|| "Không thể tìm thư mục data".to_string())?
        .join("youtube-streaming");

    std::fs::create_dir_all(&data_dir).map_err(|e| e.to_string())?;
    let settings_file = data_dir.join("settings.json");

    let mut updated_settings = settings;
    updated_settings.updated_at = chrono::Utc::now();

    let json = serde_json::to_string_pretty(&updated_settings).map_err(|e| e.to_string())?;
    std::fs::write(settings_file, json).map_err(|e| e.to_string())?;

    log::info!("Đã lưu cài đặt ứng dụng");
    Ok(())
}

#[tauri::command]
async fn test_simple_stream(
    app_handle: AppHandle,
    input_file: String,
    rtmp_url: String,
) -> Result<String, String> {
    log::info!("🧪 Testing simple stream: {} -> {}", input_file, rtmp_url);

    // Get FFmpeg path
    let ffmpeg_path = get_ffmpeg_path(&app_handle)?;
    let simple_ffmpeg = SimpleFFmpeg::new(ffmpeg_path.to_string_lossy().to_string());

    // Validate config
    let config = SimpleStreamConfig::new(input_file, rtmp_url);
    config.validate().map_err(|e| e.to_string())?;

    // Test file validation
    match simple_ffmpeg.validate_video_file(&config.input_file) {
        Ok(true) => log::info!("✅ Video file validation passed"),
        Ok(false) => return Err("❌ Video file validation failed".to_string()),
        Err(e) => return Err(format!("❌ Error validating video file: {}", e)),
    }

    // Get video duration
    let duration = simple_ffmpeg.get_video_duration(&config.input_file)
        .unwrap_or_else(|_| "Unknown".to_string());

    log::info!("📹 Video duration: {}", duration);

    // Try to start stream (but don't actually run it)
    match simple_ffmpeg.create_simple_stream(&config.input_file, &config.rtmp_url) {
        Ok(mut child) => {
            log::info!("✅ Simple stream test successful");
            // Kill the test process immediately
            let _ = child.kill();
            Ok(format!("✅ Test thành công! Video duration: {}", duration))
        }
        Err(e) => {
            log::error!("❌ Simple stream test failed: {}", e);
            Err(format!("❌ Test failed: {}", e))
        }
    }
}

#[tauri::command]
async fn optimize_for_long_stream(
    app_handle: AppHandle,
    input_file: String,
    rtmp_url: String,
) -> Result<String, String> {
    log::info!("🔧 Optimizing stream configuration for long-running: {}", input_file);

    let ffmpeg_path = get_ffmpeg_path(&app_handle).map_err(|e| e.to_string())?;
    let optimizer = LongStreamOptimizer::new(ffmpeg_path);

    // Validate configuration first
    let validation = optimizer.validate_stream_config(&input_file, &rtmp_url)
        .map_err(|e| e.to_string())?;

    if !validation.is_valid {
        let errors = validation.errors.join(", ");
        return Err(format!("❌ Validation failed: {}", errors));
    }

    // Get current settings
    let settings = load_app_settings().unwrap_or_else(|_| AppSettings::default());

    // Create optimized command (just for validation)
    let _cmd = optimizer.create_optimized_command(&input_file, &rtmp_url, &settings)
        .map_err(|e| e.to_string())?;

    let mut result = String::new();
    result.push_str("✅ Long-stream optimization ready!\n\n");
    result.push_str(&format!("📁 File size: {} MB\n", validation.file_size_mb));
    result.push_str(&format!("🎯 Target bitrate: {} kbps\n", settings.default_bitrate));
    result.push_str(&format!("🎬 Framerate: {} fps\n", settings.default_framerate));
    result.push_str("\n🔧 Optimizations applied:\n");
    result.push_str("• Adaptive bitrate control\n");
    result.push_str("• Network resilience features\n");
    result.push_str("• Enhanced error recovery\n");
    result.push_str("• Memory management\n");
    result.push_str("• Long-stream stability\n");

    if !validation.warnings.is_empty() {
        result.push_str("\n⚠️ Warnings:\n");
        for warning in validation.warnings {
            result.push_str(&format!("• {}\n", warning));
        }
    }

    Ok(result)
}

#[tauri::command]
async fn validate_stream_config(
    app_handle: AppHandle,
    input_file: String,
    rtmp_url: String,
) -> Result<String, String> {
    log::info!("🔍 Validating stream configuration: {}", input_file);

    let ffmpeg_path = get_ffmpeg_path(&app_handle).map_err(|e| e.to_string())?;
    let optimizer = LongStreamOptimizer::new(ffmpeg_path);

    let validation = optimizer.validate_stream_config(&input_file, &rtmp_url)
        .map_err(|e| e.to_string())?;

    let mut result = String::new();

    if validation.is_valid {
        result.push_str("✅ Configuration is valid!\n\n");
        result.push_str(&format!("📁 File size: {} MB\n", validation.file_size_mb));

        // Estimate network requirements
        let estimated_duration_hours = validation.file_size_mb / 100; // Rough estimate
        result.push_str(&format!("⏱️ Estimated duration: ~{} hours\n", estimated_duration_hours));

        if validation.file_size_mb > 1000 {
            result.push_str("💡 Large file detected - perfect for long streaming!\n");
        }
    } else {
        result.push_str("❌ Configuration has issues:\n\n");
        for error in validation.errors {
            result.push_str(&format!("• {}\n", error));
        }
    }

    if !validation.warnings.is_empty() {
        result.push_str("\n⚠️ Warnings:\n");
        for warning in validation.warnings {
            result.push_str(&format!("• {}\n", warning));
        }
    }

    Ok(result)
}

// Old function removed - using bundled FFmpeg instead

#[derive(Serialize, Deserialize)]
struct FFmpegStatus {
    available: bool,
    path: String,
    version: Option<String>,
}

#[tauri::command]
async fn check_ffmpeg(app_handle: AppHandle) -> Result<FFmpegStatus, String> {
    // Try bundled FFmpeg first
    let ffmpeg_path = match get_ffmpeg_path(&app_handle) {
        Ok(path) => path.to_string_lossy().to_string(),
        Err(_) => "ffmpeg".to_string(), // Fallback to system FFmpeg
    };

    match std::process::Command::new(&ffmpeg_path).arg("-version").output() {
        Ok(output) => {
            if output.status.success() {
                // Extract version from output
                let version_output = String::from_utf8_lossy(&output.stdout);
                let version = version_output
                    .lines()
                    .next()
                    .and_then(|line| {
                        if line.starts_with("ffmpeg version") {
                            line.split_whitespace().nth(2).map(|v| v.to_string())
                        } else {
                            None
                        }
                    });

                Ok(FFmpegStatus {
                    available: true,
                    path: ffmpeg_path,
                    version,
                })
            } else {
                Ok(FFmpegStatus {
                    available: false,
                    path: ffmpeg_path,
                    version: None,
                })
            }
        }
        Err(e) => {
            log::error!("Failed to check FFmpeg: {}", e);
            Ok(FFmpegStatus {
                available: false,
                path: ffmpeg_path,
                version: None,
            })
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    env_logger::init();

    log::info!("🚀 Starting YouTube Streaming App...");

    let stream_manager = Arc::new(StreamManager::new());
    let manager_for_scheduler = stream_manager.clone();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .manage(stream_manager.clone())
        .setup(move |app| {
            // Set bundled FFmpeg path
            if let Ok(ffmpeg_path) = get_ffmpeg_path(&app.handle()) {
                stream_manager.set_ffmpeg_path(ffmpeg_path.clone());
                log::info!("✅ Using bundled FFmpeg: {:?}", ffmpeg_path);
            } else {
                log::warn!("⚠️ Bundled FFmpeg not found, using system FFmpeg");
            }

            // Load existing streams
            if let Err(e) = stream_manager.load_streams() {
                eprintln!("Failed to load streams: {}", e);
            }

            // Start the scheduler in the main Tauri async context
            let manager = manager_for_scheduler.clone();
            tauri::async_runtime::spawn(async move {
                log::info!("🚀 Initializing stream scheduler...");
                manager.start_scheduler().await;
            });

            // Setup window close event handler
            if let Some(main_window) = app.get_webview_window("main") {
                let manager_for_close = stream_manager.clone();
                let window_for_emit = main_window.clone();

                main_window.on_window_event(move |event| {
                    if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                        log::info!("🚪 Window close requested, checking running streams...");

                        // Prevent default close behavior
                        api.prevent_close();

                        // Check for running streams
                        let running_streams: Vec<_> = manager_for_close.get_all_streams()
                            .into_iter()
                            .filter(|s| s.status == StreamStatus::Running)
                            .collect();

                        if running_streams.is_empty() {
                            log::info!("✅ No running streams, closing app safely");
                            std::process::exit(0);
                        } else {
                            log::warn!("⚠️ Found {} running streams, need user confirmation", running_streams.len());
                            // The frontend will handle the confirmation dialog
                            // We'll emit an event to the frontend
                            let _ = window_for_emit.emit("close-requested", running_streams);
                        }
                    }
                });
            }

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            check_license,
            get_bundled_ffmpeg_path,
            create_stream,
            get_all_streams,
            get_stream,
            start_stream,
            stop_stream,
            restart_stream,
            delete_stream,
            update_stream,
            get_stream_stats,
            get_stream_logs,
            check_running_streams,
            stop_all_streams,
            force_close_app,
            get_stream_error_details,
            test_rtmp_connection,
            check_ffmpeg,
            get_settings,
            save_settings,
            test_simple_stream,
            optimize_for_long_stream,
            validate_stream_config
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
