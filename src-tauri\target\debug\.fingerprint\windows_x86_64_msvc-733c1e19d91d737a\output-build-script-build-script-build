{"$message_type":"diagnostic","message":"linking with `link.exe` failed: exit code: 1","code":null,"level":"error","spans":[],"children":[{"message":"\"link.exe\" \"/NOLOGO\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\rustcfKyAEp\\\\symbols.o\" \"<2 object files omitted>\" \"<sysroot>\\\\lib\\\\rustlib\\\\x86_64-pc-windows-msvc\\\\lib/{libstd-*,libpanic_unwind-*,libwindows_targets-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libunwind-*,libcfg_if-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"kernel32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"dbghelp.lib\" \"/defaultlib:msvcrt\" \"/NXCOMPAT\" \"/OUT:H:\\\\YoutubeStreaming1\\\\YoutubeStreaming\\\\src-tauri\\\\target\\\\debug\\\\build\\\\windows_x86_64_msvc-733c1e19d91d737a\\\\build_script_build-733c1e19d91d737a.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/PDBALTPATH:%_PDB%\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:<sysroot>\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"link: extra operand 'H:\\\\YoutubeStreaming1\\\\YoutubeStreaming\\\\src-tauri\\\\target\\\\debug\\\\build\\\\windows_x86_64_msvc-733c1e19d91d737a\\\\build_script_build-733c1e19d91d737a.build_script_build.8b6892cfcf2d6f2b-cgu.0.rcgu.o'\nTry 'link --help' for more information.\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m: linking with `link.exe` failed: exit code: 1\u001b[0m\n"}
{"$message_type":"diagnostic","message":"`link.exe` returned an unexpected error","code":null,"level":"note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: `link.exe` returned an unexpected error\u001b[0m\n"}
{"$message_type":"diagnostic","message":"in the Visual Studio installer, ensure the \"C++ build tools\" workload is selected","code":null,"level":"note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in the Visual Studio installer, ensure the \"C++ build tools\" workload is selected\u001b[0m\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m: aborting due to 1 previous error\u001b[0m\n"}
