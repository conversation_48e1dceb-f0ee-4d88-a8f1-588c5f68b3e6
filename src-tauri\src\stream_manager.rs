use std::collections::HashMap;
use std::process::{Child, Command, Stdio};
use std::sync::{<PERSON>, Mutex};
use std::io::{<PERSON>ufRead, BufReader};
use std::thread;
use std::path::PathBuf;
use tokio::time::{interval, Duration};
use uuid::Uuid;
use chrono::{Utc, DateTime};
use anyhow::Result;

use crate::models::{StreamConfig, StreamStatus, StreamInfo, AppSettings};
use crate::ffmpeg_command_builder::{FFmpegCommandBuilder, StabilityMode, determine_stability_mode};
use crate::error_detector::{ErrorDetector, StreamError, RecoveryAction};
use crate::reconnection_manager::{ReconnectionManager, ReconnectionStrategy};

#[cfg(target_os = "windows")]
use std::os::windows::fs;

fn load_app_settings() -> Result<AppSettings, Box<dyn std::error::Error>> {
    let data_dir = dirs::data_dir()
        .ok_or("Không thể tìm thư mục data")?
        .join("youtube-streaming");

    let settings_file = data_dir.join("settings.json");

    if !settings_file.exists() {
        return Ok(AppSettings::default());
    }

    let json = std::fs::read_to_string(settings_file)?;
    let settings: AppSettings = serde_json::from_str(&json)?;
    Ok(settings)
}

pub struct StreamProcess {
    pub child: Child,
    pub config: StreamConfig,
    pub start_time: chrono::DateTime<Utc>, // FFmpeg process start time
    pub connection_time: Option<chrono::DateTime<Utc>>, // RTMP connection success time
    pub last_health_check: chrono::DateTime<Utc>,
    pub logs: Arc<Mutex<Vec<String>>>,
    pub error_reason: Option<String>,
    pub reconnect_count: u32, // Track reconnection attempts
    pub last_disconnect_time: Option<chrono::DateTime<Utc>>, // Track disconnections
    pub error_detector: ErrorDetector, // Advanced error detection
    pub stability_mode: StabilityMode, // Current stability mode
    pub last_error_type: Option<StreamError>, // Last detected error
}

#[derive(Clone)]
pub struct StreamManager {
    pub streams: Arc<Mutex<HashMap<Uuid, StreamConfig>>>,
    processes: Arc<Mutex<HashMap<Uuid, StreamProcess>>>,
    ffmpeg_path: Arc<Mutex<Option<PathBuf>>>,
    persistent_logs: Arc<Mutex<HashMap<Uuid, Vec<String>>>>, // Keep logs after stream stops
    reconnection_manager: Arc<Mutex<ReconnectionManager>>, // Intelligent reconnection
}

impl StreamManager {
    pub fn new() -> Self {
        let manager = Self {
            streams: Arc::new(Mutex::new(HashMap::new())),
            processes: Arc::new(Mutex::new(HashMap::new())),
            ffmpeg_path: Arc::new(Mutex::new(None)),
            persistent_logs: Arc::new(Mutex::new(HashMap::new())),
            reconnection_manager: Arc::new(Mutex::new(ReconnectionManager::new())),
        };

        // Load persisted streams
        if let Err(e) = manager.load_streams() {
            log::warn!("Không thể tải streams đã lưu: {}", e);
        }

        manager
    }

    pub fn set_ffmpeg_path(&self, path: PathBuf) {
        let mut ffmpeg_path = self.ffmpeg_path.lock().unwrap();
        *ffmpeg_path = Some(path);
    }

    // Enhanced stream health monitoring for long-running streams
    fn monitor_stream_health(&self, id: Uuid, logs: Arc<Mutex<Vec<String>>>) {
        let processes = self.processes.clone();
        let streams = self.streams.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(15)); // Check every 15s
            let mut lag_count = 0;
            let mut connection_issues = 0;
            let mut last_stats_time = std::time::Instant::now();
            let mut last_memory_cleanup = std::time::Instant::now();
            let memory_cleanup_interval = std::time::Duration::from_secs(3600); // Cleanup every hour

            loop {
                interval.tick().await;

                // Check if process still exists
                let process_exists = {
                    let processes_guard = processes.lock().unwrap();
                    processes_guard.contains_key(&id)
                };

                if !process_exists {
                    log::info!("🔍 Health monitor stopping for stream {}", id);
                    break;
                }

                // MEMORY CLEANUP - For long-duration streams
                if last_memory_cleanup.elapsed() >= memory_cleanup_interval {
                    log::info!("🧹 Performing memory cleanup for long-duration stream {}", id);

                    // Clear old logs to prevent memory accumulation
                    {
                        let mut logs_guard = logs.lock().unwrap();
                        // Keep only last 50 log entries to prevent memory growth
                        if logs_guard.len() > 50 {
                            let logs_vec: Vec<_> = logs_guard.iter().rev().take(50).cloned().collect();
                            logs_guard.clear();
                            logs_guard.extend(logs_vec.into_iter().rev());
                            log::info!("🧹 Cleaned old logs, kept last 50 entries");
                        }
                    }

                    last_memory_cleanup = std::time::Instant::now();
                }

                // Check recent logs for various issues
                let recent_logs = {
                    let logs_guard = logs.lock().unwrap();
                    logs_guard.iter().rev().take(20).cloned().collect::<Vec<_>>()
                };

                // Detect different types of issues
                let has_lag = recent_logs.iter().any(|log| {
                    log.contains("lag of") ||
                    log.contains("speed=0.") ||
                    log.contains("Resumed reading") ||
                    log.contains("Non-monotonous DTS")
                });

                let has_connection_issues = recent_logs.iter().any(|log| {
                    log.contains("Connection refused") ||
                    log.contains("Connection reset") ||
                    log.contains("Broken pipe") ||
                    log.contains("Network is unreachable") ||
                    log.contains("RTMP_Connect0") ||
                    log.contains("Failed to connect")
                });

                let has_encoding_issues = recent_logs.iter().any(|log| {
                    log.contains("frame dropped") ||
                    log.contains("buffer underrun") ||
                    log.contains("encoder queue full")
                });

                // Handle connection issues first (most critical)
                if has_connection_issues {
                    connection_issues += 1;
                    log::warn!("🌐 Stream {} connection issues detected (count: {})", id, connection_issues);

                    if connection_issues >= 2 {
                        log::error!("🚨 Stream {} has persistent connection issues", id);
                        // Mark for potential reconnection
                        if let Ok(mut streams_guard) = streams.lock() {
                            if let Some(stream_config) = streams_guard.get_mut(&id) {
                                if stream_config.status == StreamStatus::Running {
                                    log::info!("🔄 Stream {} may need reconnection due to network issues", id);
                                }
                            }
                        }
                    }
                } else {
                    connection_issues = 0; // Reset if connection is stable
                }

                // Handle lag issues
                if has_lag {
                    lag_count += 1;
                    log::warn!("⚠️ Stream {} lag detected (count: {})", id, lag_count);

                    if lag_count >= 5 {
                        log::error!("🚨 Stream {} has persistent lag - consider reducing bitrate", id);
                        log::info!("💡 Suggestion: Lower bitrate or check network bandwidth");
                    }
                } else {
                    lag_count = 0; // Reset counter if no lag
                }

                // Handle encoding issues
                if has_encoding_issues {
                    log::warn!("🎬 Stream {} encoding issues detected - may need optimization", id);
                }
            }
        });
    }

    // Save streams when manager is dropped (app shutdown)
    pub fn shutdown(&self) {
        log::info!("StreamManager shutdown - saving current state...");

        // Mark all running streams as stopped
        {
            let mut streams = self.streams.lock().unwrap();
            let mut updated = false;

            for stream in streams.values_mut() {
                if stream.status == StreamStatus::Running {
                    log::info!("Đánh dấu stream '{}' từ Running -> Stopped (app shutdown)", stream.name);
                    stream.status = StreamStatus::Stopped;
                    if stream.ended_at.is_none() {
                        stream.ended_at = Some(Utc::now());
                    }
                    updated = true;
                }
            }

            if updated {
                drop(streams); // Release lock before saving
                if let Err(e) = self.save_streams() {
                    log::error!("Không thể lưu streams khi shutdown: {}", e);
                } else {
                    log::info!("Đã lưu trạng thái streams khi shutdown");
                }
            }
        }
    }

    fn get_data_dir() -> Result<std::path::PathBuf> {
        let data_dir = dirs::data_dir()
            .ok_or_else(|| anyhow::anyhow!("Không thể tìm thư mục data"))?
            .join("youtube-streaming");

        std::fs::create_dir_all(&data_dir)?;
        Ok(data_dir)
    }

    pub fn save_streams(&self) -> Result<()> {
        let data_dir = Self::get_data_dir()?;
        let streams_file = data_dir.join("streams.json");

        let streams = self.streams.lock().unwrap();

        // Create a copy of streams with current status preserved
        let mut streams_data: Vec<StreamConfig> = Vec::new();
        for stream in streams.values() {
            let mut stream_copy = stream.clone();

            // Ensure stopped streams don't revert to Scheduled
            if stream_copy.status == StreamStatus::Running {
                // Check if process is actually running
                let processes = self.processes.lock().unwrap();
                if !processes.contains_key(&stream_copy.id) {
                    // Process not found, mark as stopped
                    stream_copy.status = StreamStatus::Stopped;
                    stream_copy.ended_at = Some(Utc::now());
                }
            }

            streams_data.push(stream_copy);
        }

        let json = serde_json::to_string_pretty(&streams_data)?;
        std::fs::write(streams_file, json)?;

        log::info!("Đã lưu {} streams vào file với trạng thái hiện tại", streams_data.len());
        Ok(())
    }

    pub fn load_streams(&self) -> Result<()> {
        let data_dir = Self::get_data_dir()?;
        let streams_file = data_dir.join("streams.json");

        if !streams_file.exists() {
            return Ok(());
        }

        let json = std::fs::read_to_string(streams_file)?;
        let mut streams_data: Vec<StreamConfig> = serde_json::from_str(&json)?;

        // Clean up streams on app restart
        let mut updated_count = 0;
        for stream in &mut streams_data {
            // Any stream marked as Running should be marked as Stopped on app restart
            // because processes don't survive app restart
            if stream.status == StreamStatus::Running {
                log::info!("Đánh dấu stream '{}' từ Running -> Stopped (app restart)", stream.name);
                stream.status = StreamStatus::Stopped;
                if stream.ended_at.is_none() {
                    stream.ended_at = Some(Utc::now());
                }
                updated_count += 1;
            }
        }

        let mut streams = self.streams.lock().unwrap();
        for stream in streams_data {
            streams.insert(stream.id, stream);
        }
        drop(streams); // Release lock before saving

        // Save the cleaned up state immediately
        if updated_count > 0 {
            log::info!("Đã cập nhật {} streams từ Running -> Stopped", updated_count);
            if let Err(e) = self.save_streams() {
                log::warn!("Không thể lưu streams sau khi cleanup: {}", e);
            }
        }

        let final_count = {
            let streams = self.streams.lock().unwrap();
            streams.len()
        };

        log::info!("Đã tải {} streams từ file", final_count);

        // Debug: Log current stream statuses
        {
            let streams = self.streams.lock().unwrap();
            for (id, stream) in streams.iter() {
                log::info!("Stream loaded: '{}' (ID: {}) - Status: {:?}", stream.name, id, stream.status);
            }
        }

        Ok(())
    }

    pub fn add_stream(&self, config: StreamConfig) -> Result<Uuid> {
        // Check for duplicate RTMP URL/stream key
        {
            let streams = self.streams.lock().unwrap();
            for (_, existing_stream) in streams.iter() {
                if existing_stream.rtmp_url == config.rtmp_url {
                    return Err(anyhow::anyhow!("Mã sự kiện YouTube này đã được sử dụng trong luồng '{}'", existing_stream.name));
                }
            }
        }

        let id = config.id;
        let mut streams = self.streams.lock().unwrap();
        streams.insert(id, config);
        drop(streams); // Release lock before saving

        // Save to file
        if let Err(e) = self.save_streams() {
            log::warn!("Không thể lưu streams: {}", e);
        }

        log::info!("Đã tạo luồng stream: {}", id);
        Ok(id)
    }

    pub fn get_all_streams(&self) -> Vec<StreamInfo> {
        let streams = self.streams.lock().unwrap();
        streams.values().map(|config| config.to_info()).collect()
    }

    pub fn get_stream(&self, id: Uuid) -> Option<StreamInfo> {
        let streams = self.streams.lock().unwrap();
        streams.get(&id).map(|config| config.to_info())
    }

    pub fn start_stream(&self, id: Uuid) -> Result<()> {
        let mut streams = self.streams.lock().unwrap();
        let mut processes = self.processes.lock().unwrap();

        if let Some(config) = streams.get_mut(&id) {
            if config.status == StreamStatus::Running {
                return Err(anyhow::anyhow!("Luồng stream đã đang chạy"));
            }

            // Validate input file exists
            if !std::path::Path::new(&config.input_file).exists() {
                return Err(anyhow::anyhow!("File video không tồn tại: {}", config.input_file));
            }

            // Validate RTMP URL format
            if !config.rtmp_url.starts_with("rtmp://") && !config.rtmp_url.starts_with("rtmps://") {
                return Err(anyhow::anyhow!("RTMP URL không hợp lệ. Phải bắt đầu với rtmp:// hoặc rtmps://"));
            }

            log::info!("🔗 Validating RTMP URL: {}", config.rtmp_url);

            // Load app settings for encoding parameters
            let settings = load_app_settings().unwrap_or_else(|_| AppSettings::default());

            // Get FFmpeg path
            let ffmpeg_path = {
                let path_guard = self.ffmpeg_path.lock().unwrap();
                match path_guard.as_ref() {
                    Some(path) => path.clone(),
                    None => {
                        // Fallback to system FFmpeg if bundled not available
                        PathBuf::from("ffmpeg")
                    }
                }
            };

            log::info!("🎯 Starting FFmpeg with path: {}", ffmpeg_path.display());
            log::info!("📁 Input file: {}", config.input_file);
            log::info!("📡 RTMP URL: {}", config.rtmp_url);

            // Determine stability mode based on previous errors (if any)
            let stability_mode = StabilityMode::HighQuality; // Start with high quality, will adapt on errors

            // Create unified FFmpeg command builder
            let command_builder = FFmpegCommandBuilder::new(ffmpeg_path, &settings, stability_mode);

            // SAFE FILE PATH HANDLING - Xử lý đường dẫn file an toàn
            log::info!("🎬 Sử dụng file video: {}", config.input_file);

            // Validate file exists
            let file_path = std::path::Path::new(&config.input_file);
            if !file_path.exists() {
                log::error!("❌ File không tồn tại: {}", config.input_file);
                return Err(anyhow::anyhow!("File không tồn tại: {}", config.input_file));
            }

            // Log basic file info
            if let Ok(metadata) = file_path.metadata() {
                log::info!("📁 File size: {}MB", metadata.len() / 1024 / 1024);
            }

            // AGGRESSIVE UNICODE PATH HANDLING - Xử lý triệt để đường dẫn Unicode
            let safe_input_path = if cfg!(target_os = "windows") {
                // Check if path contains non-ASCII characters
                if config.input_file.chars().any(|c| !c.is_ascii()) {
                    log::warn!("⚠️ File path contains Unicode characters: {}", config.input_file);

                    // Try multiple approaches to get a safe path
                    if let Ok(short_path) = get_short_path(&config.input_file) {
                        if short_path != config.input_file && !short_path.chars().any(|c| !c.is_ascii()) {
                            log::info!("✅ Using short path: {}", short_path);
                            short_path
                        } else {
                            log::warn!("⚠️ Short path still contains Unicode or is same as original");
                            // Try copying file to temp location with ASCII name
                            match copy_to_temp_ascii(&config.input_file) {
                                Ok(temp_path) => {
                                    log::info!("✅ Using temporary ASCII path: {}", temp_path);
                                    temp_path
                                }
                                Err(e) => {
                                    log::error!("❌ Failed to create temp ASCII file: {}", e);
                                    return Err(anyhow::anyhow!("Cannot process file with Unicode characters: {}", config.input_file));
                                }
                            }
                        }
                    } else {
                        log::warn!("⚠️ Could not get short path, trying temp ASCII copy");
                        // Try copying file to temp location with ASCII name
                        match copy_to_temp_ascii(&config.input_file) {
                            Ok(temp_path) => {
                                log::info!("✅ Using temporary ASCII path: {}", temp_path);
                                temp_path
                            }
                            Err(e) => {
                                log::error!("❌ Failed to create temp ASCII file: {}", e);
                                return Err(anyhow::anyhow!("Cannot process file with Unicode characters: {}", config.input_file));
                            }
                        }
                    }
                } else {
                    log::info!("✅ File path is ASCII-safe: {}", config.input_file);
                    config.input_file.clone()
                }
            } else {
                config.input_file.clone()
            };

            // BUILD FFMPEG COMMAND using unified builder
            let mut cmd = match command_builder.build_streaming_command(&safe_input_path, &config.rtmp_url) {
                Ok(cmd) => cmd,
                Err(e) => {
                    log::error!("❌ Failed to build FFmpeg command: {}", e);
                    return Err(anyhow::anyhow!("Failed to build FFmpeg command: {}", e));
                }
            };

            log::info!("Đang khởi động FFmpeg process...");
            let mut child = match cmd.spawn() {
                Ok(child) => {
                    log::info!("FFmpeg process đã khởi động thành công với PID: {}", child.id());
                    child
                }
                Err(e) => {
                    log::error!("Không thể khởi động FFmpeg: {}", e);
                    return Err(anyhow::anyhow!("Không thể khởi động FFmpeg: {}. Kiểm tra FFmpeg đã được cài đặt chưa.", e));
                }
            };

            let now = Utc::now();
            let logs = Arc::new(Mutex::new(Vec::new()));

            // Capture stderr for logging and error detection
            if let Some(stderr) = child.stderr.take() {
                let logs_clone = logs.clone();
                let stream_name = config.name.clone();
                let streams_clone = self.streams.clone();
                let processes_clone = self.processes.clone();
                let stream_id = id;

                let persistent_logs_clone = self.persistent_logs.clone();

                thread::spawn(move || {
                    let reader = BufReader::new(stderr);
                    let mut connection_established = false;
                    let mut error_detector = ErrorDetector::new();

                    for line in reader.lines() {
                        if let Ok(line) = line {
                            let timestamp = chrono::Utc::now().format("%H:%M:%S").to_string();
                            let log_entry = format!("[{}] [{}] {}", timestamp, stream_name, line);

                            // Check for RTMP connection success - enhanced patterns
                            if !connection_established && (
                                line.contains("Server bandwidth") ||
                                line.contains("Metadata:") ||
                                line.contains("Publishing") ||
                                line.contains("Stream #0:0") ||
                                line.contains("fps=") ||
                                line.contains("bitrate=") ||
                                line.contains("time=") ||
                                (line.contains("frame=") && line.contains("fps=")) ||
                                line.contains("Parsed_") ||
                                line.contains("encoder") ||
                                line.contains("muxer")
                            ) {
                                connection_established = true;
                                let connection_time = chrono::Utc::now();
                                log::info!("✅ RTMP connection established: {}", log_entry);

                                // Update stream config with actual start time (when connection succeeds)
                                if let Ok(mut streams) = streams_clone.lock() {
                                    if let Some(config) = streams.get_mut(&stream_id) {
                                        config.started_at = Some(connection_time);
                                        log::info!("🕐 Stream '{}' timing started at: {}",
                                            config.name, connection_time.format("%H:%M:%S UTC"));
                                    }
                                }

                                // Update process with connection time
                                if let Ok(mut processes) = processes_clone.lock() {
                                    if let Some(process) = processes.get_mut(&stream_id) {
                                        process.connection_time = Some(connection_time);
                                    }
                                }
                            }

                            // ADVANCED ERROR DETECTION using new system
                            if let Some(detected_error) = error_detector.detect_error(&line) {
                                log::error!("🚨 Detected streaming error: {:?} - {}", detected_error, log_entry);

                                // Update process with error information
                                if let Ok(mut processes) = processes_clone.lock() {
                                    if let Some(process) = processes.get_mut(&stream_id) {
                                        process.last_error_type = Some(detected_error.clone());
                                        process.error_reason = Some(line.clone());
                                    }
                                }

                                // Check if recovery action is needed
                                if error_detector.should_trigger_recovery(&detected_error) {
                                    log::error!("🚨 Recovery action needed for error: {:?}", detected_error);

                                    // Mark stream as error if critical
                                    if error_detector.is_critical_error(&detected_error) {
                                        if let Ok(mut streams) = streams_clone.lock() {
                                            if let Some(config) = streams.get_mut(&stream_id) {
                                                config.status = StreamStatus::Error;
                                                log::error!("🚨 Stream '{}' marked as ERROR due to critical error", stream_name);
                                            }
                                        }
                                    }
                                }
                            } else {
                                // Log all other messages as debug (now with info level we get more details)
                                log::debug!("FFmpeg: {}", log_entry);
                            }

                            // IMPROVED MEMORY MANAGEMENT - More frequent cleanup
                            if let Ok(mut logs) = logs_clone.lock() {
                                logs.push(log_entry.clone());
                                if logs.len() > 100 { // Reduced from 1000 to 100
                                    logs.drain(0..50); // Clean half when limit reached
                                }
                            }

                            // Store in persistent logs (survives stream stop)
                            if let Ok(mut persistent) = persistent_logs_clone.lock() {
                                let stream_logs = persistent.entry(stream_id).or_insert_with(Vec::new);
                                stream_logs.push(log_entry);
                                if stream_logs.len() > 200 { // Reduced from 500 to 200
                                    stream_logs.drain(0..100); // Clean half when limit reached
                                }
                            }
                        }
                    }

                    // Log final status
                    let final_message = if !connection_established {
                        format!("❌ RTMP connection was never established for stream: {}", stream_name)
                    } else {
                        format!("ℹ️ Stream '{}' ended normally", stream_name)
                    };

                    log::error!("{}", final_message);

                    // Store final message in persistent logs
                    if let Ok(mut persistent) = persistent_logs_clone.lock() {
                        let stream_logs = persistent.entry(stream_id).or_insert_with(Vec::new);
                        stream_logs.push(format!("[{}] {}", chrono::Utc::now().format("%H:%M:%S"), final_message));
                    }
                });
            }

            config.status = StreamStatus::Running;
            // Don't set started_at here - wait for RTMP connection success
            // config.started_at will be set when connection is established

            let stream_process = StreamProcess {
                child,
                config: config.clone(),
                start_time: now, // FFmpeg process start time
                connection_time: None, // Will be set when RTMP connects
                last_health_check: now,
                logs: logs.clone(),
                error_reason: None,
                reconnect_count: 0, // Initialize reconnect counter
                last_disconnect_time: None, // No disconnection yet
                error_detector: ErrorDetector::new(), // Advanced error detection
                stability_mode: stability_mode, // Current stability mode
                last_error_type: None, // No errors yet
            };

            processes.insert(id, stream_process);

            // Start health monitoring
            self.monitor_stream_health(id, logs.clone());

            log::info!("✅ Đã bắt đầu luồng stream: {} ({})", config.name, id);
            log::info!("📁 Input file: {}", config.input_file);
            log::info!("📡 RTMP URL: {}", config.rtmp_url);
            log::info!("⏳ Đang chờ kết nối RTMP thành công để bắt đầu đếm thời gian...");
            Ok(())
        } else {
            Err(anyhow::anyhow!("Stream not found"))
        }
    }

    pub fn stop_stream(&self, id: Uuid) -> Result<()> {
        let stream_name = {
            let streams = self.streams.lock().unwrap();
            streams.get(&id).map(|s| s.name.clone())
        };

        let mut streams = self.streams.lock().unwrap();
        let mut processes = self.processes.lock().unwrap();

        // Check if stream exists
        if !streams.contains_key(&id) {
            return Err(anyhow::anyhow!("Stream với ID {} không tồn tại", id));
        }

        // Check if process exists and kill it
        let (process_killed, removed_process) = if let Some(mut process) = processes.remove(&id) {
            match process.child.kill() {
                Ok(_) => {
                    log::info!("✅ Đã dừng FFmpeg process cho stream {}", id);
                    (true, Some(process))
                }
                Err(e) => {
                    log::warn!("⚠️ Không thể dừng process cho stream {}: {}", id, e);
                    // Continue anyway to update stream status
                    (false, Some(process))
                }
            }
        } else {
            log::warn!("⚠️ Không tìm thấy process cho stream {}, chỉ cập nhật trạng thái", id);
            (false, None)
        };

        // Update stream status and save error info if any
        if let Some(config) = streams.get_mut(&id) {
            let now = Utc::now();

            // Check if stream had errors
            let error_reason = if let Some(process) = removed_process.as_ref() {
                process.error_reason.clone()
            } else {
                None
            };

            if error_reason.is_some() {
                config.status = StreamStatus::Error;
                log::error!("❌ Stream '{}' stopped with error: {}",
                    config.name, error_reason.as_ref().unwrap());
            } else {
                config.status = StreamStatus::Stopped;
            }

            config.ended_at = Some(now);

            // Log duration if stream was running
            let duration_info = if let Some(started_at) = config.started_at {
                let duration = (now - started_at).num_seconds();
                format!(" (đã chạy {}s)", duration)
            } else {
                String::new()
            };

            let name = stream_name.unwrap_or_else(|| "Unknown".to_string());
            if process_killed {
                log::info!("✅ Đã dừng hoàn toàn stream: '{}' ({}){}", name, id, duration_info);
            } else {
                log::info!("⚠️ Đã cập nhật trạng thái stream: '{}' ({}) thành Stopped{}", name, id, duration_info);
            }

            // Add final log entry to persistent logs
            let final_log = format!("[{}] Stream ended - Status: {:?}, Duration: {}s",
                now.format("%H:%M:%S"), config.status,
                config.started_at.map(|s| (now - s).num_seconds()).unwrap_or(0));

            let mut persistent = self.persistent_logs.lock().unwrap();
            let stream_logs = persistent.entry(id).or_insert_with(Vec::new);
            stream_logs.push(final_log);
        }

        drop(streams); // Release locks before saving
        drop(processes);

        // Save state immediately
        if let Err(e) = self.save_streams() {
            log::warn!("⚠️ Không thể lưu streams sau khi dừng: {}", e);
        }

        Ok(())
    }

    pub fn delete_stream(&self, id: Uuid) -> Result<()> {
        // Check if stream is currently running
        {
            let processes = self.processes.lock().unwrap();
            if processes.contains_key(&id) {
                return Err(anyhow::anyhow!("Không thể xóa luồng đang chạy. Vui lòng dừng luồng trước khi xóa."));
            }
        }

        let mut streams = self.streams.lock().unwrap();
        if streams.remove(&id).is_some() {
            drop(streams); // Release lock before saving

            // Save to file
            if let Err(e) = self.save_streams() {
                log::warn!("Không thể lưu streams sau khi xóa: {}", e);
            }

            log::info!("Đã xóa luồng stream: {}", id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Không tìm thấy luồng stream để xóa"))
        }
    }

    pub async fn start_scheduler(&self) {
        log::info!("🕐 Starting stream scheduler...");
        let manager = self.clone();
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(5)); // Check every 5 seconds for better responsiveness
            let mut tick_count = 0;

            loop {
                interval.tick().await;
                tick_count += 1;

                if tick_count % 12 == 0 { // Log every minute
                    log::debug!("⏰ Scheduler tick #{} - checking streams...", tick_count);
                }

                manager.check_scheduled_streams().await;
                manager.check_stream_timeouts().await;
                let _ = manager.monitor_streams();
            }
        });
        log::info!("✅ Stream scheduler started successfully");
    }

    pub fn monitor_streams(&self) -> Result<()> {
        let mut streams = self.streams.lock().unwrap();
        let mut processes = self.processes.lock().unwrap();
        let now = Utc::now();

        let mut dead_streams = Vec::new();
        let mut streams_to_reconnect = Vec::new();

        for (id, process) in processes.iter_mut() {
            // Check if process is still alive
            match process.child.try_wait() {
                Ok(Some(status)) => {
                    // Process has exited - check if it was expected or error
                    if status.success() {
                        log::warn!("Luồng stream {} đã kết thúc bình thường (có thể do hết video và không loop)", id);
                        log::warn!("Lưu ý: Video có thể đã phát hết. Hãy restart stream để tiếp tục.");
                    } else {
                        log::error!("Luồng stream {} đã thoát với lỗi: {:?}", id, status);

                        // Check if this is an RTMP disconnection that should be auto-reconnected
                        if let Some(stream_config) = streams.get(id) {
                            if stream_config.status == StreamStatus::Running && process.reconnect_count < 10 {
                                log::info!("🔄 Attempting auto-reconnect for stream '{}' (attempt #{})", stream_config.name, process.reconnect_count + 1);
                                streams_to_reconnect.push((*id, stream_config.clone()));
                                continue; // Don't mark as dead, will be reconnected
                            }
                        }
                    }
                    dead_streams.push(*id);
                }
                Ok(None) => {
                    // Process is still running
                    process.last_health_check = now;
                    log::debug!("Luồng stream {} đang hoạt động tốt", id);
                }
                Err(e) => {
                    log::error!("Lỗi khi kiểm tra luồng stream {}: {}", id, e);
                    dead_streams.push(*id);
                }
            }
        }

        // Clean up dead streams
        for id in dead_streams {
            if let Some(config) = streams.get_mut(&id) {
                config.status = StreamStatus::Error;
                log::info!("Đã đánh dấu luồng stream {} là lỗi - có thể do video đã phát hết", id);
                log::info!("Hãy click nút Restart (🔄) để tiếp tục streaming");
            }
            processes.remove(&id);
        }

        // Drop locks before attempting reconnections
        drop(streams);
        drop(processes);

        // Handle auto-reconnections
        for (stream_id, stream_config) in streams_to_reconnect {
            log::info!("🔄 Auto-reconnecting stream '{}' after RTMP disconnection...", stream_config.name);

            // Wait a bit before reconnecting to avoid rapid reconnection loops
            std::thread::sleep(std::time::Duration::from_secs(2));

            // Attempt to restart the stream
            if let Err(e) = self.start_stream(stream_id) {
                log::error!("❌ Auto-reconnect failed for stream '{}': {}", stream_config.name, e);

                // Mark as error if reconnect fails
                if let Ok(mut streams) = self.streams.lock() {
                    if let Some(config) = streams.get_mut(&stream_id) {
                        config.status = StreamStatus::Error;
                    }
                }
            } else {
                log::info!("✅ Auto-reconnect successful for stream '{}'", stream_config.name);
            }
        }

        Ok(())
    }

    pub fn get_stream_stats(&self, id: Uuid) -> Option<crate::models::StreamStats> {
        let processes = self.processes.lock().unwrap();
        if let Some(process) = processes.get(&id) {
            // Use connection_time if available, otherwise fall back to start_time
            let reference_time = process.connection_time.unwrap_or(process.start_time);
            let duration = Utc::now().signed_duration_since(reference_time);
            Some(crate::models::StreamStats {
                duration_seconds: duration.num_seconds(),
                is_healthy: true,
                last_check: process.last_health_check,
            })
        } else {
            None
        }
    }

    pub fn get_all_logs(&self) -> Vec<String> {
        let mut all_logs = Vec::new();

        // Get logs from running processes
        let processes = self.processes.lock().unwrap();
        for (id, process) in processes.iter() {
            if let Ok(logs) = process.logs.lock() {
                for log in logs.iter() {
                    all_logs.push(format!("[{}] {}", id, log));
                }
            }
        }

        // Get logs from persistent storage for stopped streams
        let persistent = self.persistent_logs.lock().unwrap();
        for (id, logs) in persistent.iter() {
            // Only add if not already in running processes
            if !processes.contains_key(id) {
                for log in logs.iter() {
                    all_logs.push(format!("[STOPPED-{}] {}", id, log));
                }
            }
        }

        all_logs
    }

    async fn check_scheduled_streams(&self) {
        let now = Utc::now();
        let streams_to_start: Vec<(Uuid, String, DateTime<Utc>)> = {
            let streams = self.streams.lock().unwrap();
            streams
                .values()
                .filter_map(|config| {
                    if config.status == StreamStatus::Scheduled {
                        if let Some(start_time) = config.start_time {
                            if start_time <= now {
                                log::info!("📅 Found scheduled stream '{}' ready to start (scheduled: {}, now: {})",
                                    config.name, start_time.format("%H:%M:%S"), now.format("%H:%M:%S"));
                                return Some((config.id, config.name.clone(), start_time));
                            }
                        }
                    }
                    None
                })
                .collect()
        };

        for (stream_id, stream_name, scheduled_time) in streams_to_start {
            log::info!("🚀 Auto-starting scheduled stream '{}' (was scheduled for {})",
                stream_name, scheduled_time.format("%H:%M:%S"));

            if let Err(e) = self.start_stream(stream_id) {
                log::error!("❌ Không thể bắt đầu luồng stream đã lên lịch '{}' ({}): {}", stream_name, stream_id, e);
                // Mark as error
                if let Some(config) = self.streams.lock().unwrap().get_mut(&stream_id) {
                    config.status = StreamStatus::Error;
                }
            } else {
                log::info!("✅ Successfully auto-started scheduled stream '{}'", stream_name);
            }
        }
    }

    async fn check_stream_timeouts(&self) {
        let now = Utc::now();
        let mut streams_to_stop = Vec::new();


        // Single pass to check all timeout conditions
        {
            let streams = self.streams.lock().unwrap();
            let processes = self.processes.lock().unwrap();

            for (id, stream) in streams.iter() {
                // Only check running streams that have active processes
                if stream.status != StreamStatus::Running || !processes.contains_key(id) {
                    continue;
                }

                let should_stop = if let Some(end_time) = &stream.end_time {
                    // Fixed end time check - ALWAYS respect scheduled end time regardless of reconnects
                    now >= *end_time
                } else if let (Some(duration_seconds), Some(started_at)) = (stream.duration_seconds, &stream.started_at) {
                    // Duration-based timeout check - count from ORIGINAL start time, not reconnect time
                    let elapsed = now.signed_duration_since(*started_at).num_seconds();
                    elapsed >= duration_seconds as i64
                } else {
                    // No timeout configured or RTMP not connected yet
                    false
                };

                if should_stop {
                    streams_to_stop.push((*id, stream.name.clone()));
                    let duration_info = if let Some(started_at) = &stream.started_at {
                        let elapsed = now.signed_duration_since(*started_at).num_seconds();
                        format!(" (đã stream {}s)", elapsed)
                    } else {
                        " (chưa kết nối RTMP)".to_string()
                    };
                    log::info!("🛑 Stream '{}' ({}) đã hết thời gian hẹn{}, sẽ được dừng tự động",
                        stream.name, id, duration_info);
                }
            }
        }

        // Stop expired streams (one by one to avoid conflicts)
        for (stream_id, stream_name) in streams_to_stop {
            match self.stop_stream(stream_id) {
                Ok(_) => {
                    log::info!("✅ Đã dừng stream '{}' theo lịch hẹn", stream_name);
                }
                Err(e) => {
                    log::error!("❌ Không thể dừng stream '{}' hết thời gian: {}", stream_name, e);
                }
            }
        }
    }

    pub fn update_stream(&self, id: Uuid, name: String, input_file: String, rtmp_url: String, notes: Option<String>) -> Result<()> {
        // Check if stream is currently running
        {
            let processes = self.processes.lock().unwrap();
            if processes.contains_key(&id) {
                return Err(anyhow::anyhow!("Không thể chỉnh sửa luồng đang chạy. Vui lòng dừng luồng trước."));
            }
        }

        // Check for duplicate RTMP URL (excluding current stream)
        {
            let streams = self.streams.lock().unwrap();
            for (existing_id, existing_stream) in streams.iter() {
                if *existing_id != id && existing_stream.rtmp_url == rtmp_url {
                    return Err(anyhow::anyhow!("Mã sự kiện YouTube này đã được sử dụng trong luồng '{}'", existing_stream.name));
                }
            }
        }

        let mut streams = self.streams.lock().unwrap();
        if let Some(stream) = streams.get_mut(&id) {
            stream.name = name;
            stream.input_file = input_file;
            stream.rtmp_url = rtmp_url;
            stream.notes = notes;
            log::info!("Đã cập nhật luồng stream: {}", id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("Không tìm thấy luồng stream"))
        }
    }

    pub fn get_stream_logs(&self, id: Uuid) -> Vec<String> {
        // First try to get logs from running process
        let processes = self.processes.lock().unwrap();
        if let Some(process) = processes.get(&id) {
            if let Ok(logs) = process.logs.lock() {
                return logs.clone();
            }
        }

        // If not running, get from persistent logs
        let persistent = self.persistent_logs.lock().unwrap();
        if let Some(logs) = persistent.get(&id) {
            logs.clone()
        } else {
            Vec::new()
        }
    }
}

// Safe path handling for FFmpeg input files
fn get_safe_path(file_path: &str) -> Result<String, Box<dyn std::error::Error>> {
    #[cfg(target_os = "windows")]
    {
        // Check if path contains non-ASCII characters
        if file_path.chars().any(|c| !c.is_ascii()) {
            log::info!("🔧 File path contains Unicode characters, attempting to get safe path");

            // Try to get short path first
            match get_short_path(file_path) {
                Ok(short_path) => {
                    if short_path != file_path {
                        log::info!("✅ Got short path: {}", short_path);
                        return Ok(short_path);
                    }
                }
                Err(e) => {
                    log::warn!("⚠️ Could not get short path: {}", e);
                }
            }

            // If short path fails, try to escape the path with quotes
            let escaped_path = format!("\"{}\"", file_path);
            log::info!("🔧 Using escaped path: {}", escaped_path);
            return Ok(escaped_path);
        }

        // Path is ASCII-safe, return as-is
        Ok(file_path.to_string())
    }

    #[cfg(not(target_os = "windows"))]
    {
        // On non-Windows systems, just return the original path
        Ok(file_path.to_string())
    }
}

// Windows-specific function to get short path names for files with special characters
#[cfg(target_os = "windows")]
fn get_short_path(long_path: &str) -> Result<String, Box<dyn std::error::Error>> {
    use std::ffi::OsString;
    use std::os::windows::ffi::{OsStringExt, OsStrExt};

    // Try to use Windows API to get short path
    let wide_path: Vec<u16> = OsString::from(long_path)
        .encode_wide()
        .chain(std::iter::once(0))
        .collect();

    let mut buffer = vec![0u16; 260];
    let result = unsafe {
        winapi::um::fileapi::GetShortPathNameW(
            wide_path.as_ptr(),
            buffer.as_mut_ptr(),
            buffer.len() as u32,
        )
    };

    if result == 0 || result as usize >= buffer.len() {
        return Err("Failed to get short path".into());
    }

    let short_path = OsString::from_wide(&buffer[..result as usize])
        .to_string_lossy()
        .to_string();

    Ok(short_path)
}

#[cfg(not(target_os = "windows"))]
fn get_short_path(long_path: &str) -> Result<String, Box<dyn std::error::Error>> {
    Ok(long_path.to_string())
}

// Copy file to temp directory with ASCII-only name
fn copy_to_temp_ascii(original_path: &str) -> Result<String, Box<dyn std::error::Error>> {
    use std::fs;
    use std::path::Path;

    let original_file = Path::new(original_path);
    if !original_file.exists() {
        return Err("Original file does not exist".into());
    }

    // Get file extension
    let extension = original_file
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("mp4");

    // Create temp directory
    let temp_dir = std::env::temp_dir().join("youtube_streaming");
    fs::create_dir_all(&temp_dir)?;

    // Generate ASCII-only filename using timestamp
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();

    let temp_filename = format!("stream_input_{}.{}", timestamp, extension);
    let temp_path = temp_dir.join(temp_filename);

    // Copy file to temp location
    log::info!("📁 Copying file to temp ASCII path...");
    fs::copy(original_path, &temp_path)?;

    let temp_path_str = temp_path.to_string_lossy().to_string();
    log::info!("✅ File copied to: {}", temp_path_str);

    Ok(temp_path_str)
}
