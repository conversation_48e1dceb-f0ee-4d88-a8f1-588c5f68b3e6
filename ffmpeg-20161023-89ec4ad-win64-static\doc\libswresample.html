<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 5.2, http://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      Libswresample Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      Libswresample Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<a name="SEC_Contents"></a>
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a name="toc-Description" href="#Description">1 Description</a></li>
  <li><a name="toc-See-Also" href="#See-Also">2 See Also</a></li>
  <li><a name="toc-Authors" href="#Authors">3 Authors</a></li>
</ul>
</div>


<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>The libswresample library performs highly optimized audio resampling,
rematrixing and sample format conversion operations.
</p>
<p>Specifically, this library performs the following conversions:
</p>
<ul>
<li> <em>Resampling</em>: is the process of changing the audio rate, for
example from a high sample rate of 44100Hz to 8000Hz. Audio
conversion from high to low sample rate is a lossy process. Several
resampling options and algorithms are available.

</li><li> <em>Format conversion</em>: is the process of converting the type of
samples, for example from 16-bit signed samples to unsigned 8-bit or
float samples. It also handles packing conversion, when passing from
packed layout (all samples belonging to distinct channels interleaved
in the same buffer), to planar layout (all samples belonging to the
same channel stored in a dedicated buffer or &quot;plane&quot;).

</li><li> <em>Rematrixing</em>: is the process of changing the channel layout, for
example from stereo to mono. When the input channels cannot be mapped
to the output streams, the process is lossy, since it involves
different gain factors and mixing.
</li></ul>

<p>Various other audio conversions (e.g. stretching and padding) are
enabled through dedicated options.
</p>

<a name="See-Also"></a>
<h2 class="chapter">2 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffmpeg.html">ffmpeg</a>, <a href="ffplay.html">ffplay</a>, <a href="ffprobe.html">ffprobe</a>, <a href="ffserver.html">ffserver</a>,
<a href="ffmpeg-resampler.html">ffmpeg-resampler</a>,
<a href="libavutil.html">libavutil</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">3 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(git://source.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="http://source.ffmpeg.org">http://source.ffmpeg.org</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>


      <p style="font-size: small;">
        This document was generated using <a href="http://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
