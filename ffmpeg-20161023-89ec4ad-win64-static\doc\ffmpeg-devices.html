<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 5.2, http://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Devices Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Devices Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<a name="SEC_Contents"></a>
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a name="toc-Description" href="#Description">1 Description</a></li>
  <li><a name="toc-Device-Options" href="#Device-Options">2 Device Options</a></li>
  <li><a name="toc-Input-Devices" href="#Input-Devices">3 Input Devices</a>
  <ul class="no-bullet">
    <li><a name="toc-alsa" href="#alsa">3.1 alsa</a>
    <ul class="no-bullet">
      <li><a name="toc-Options" href="#Options">3.1.1 Options</a></li>
    </ul></li>
    <li><a name="toc-avfoundation" href="#avfoundation">3.2 avfoundation</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-1" href="#Options-1">3.2.1 Options</a></li>
      <li><a name="toc-Examples" href="#Examples">3.2.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-bktr" href="#bktr">3.3 bktr</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-2" href="#Options-2">3.3.1 Options</a></li>
    </ul></li>
    <li><a name="toc-decklink" href="#decklink">3.4 decklink</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-3" href="#Options-3">3.4.1 Options</a></li>
      <li><a name="toc-Examples-1" href="#Examples-1">3.4.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-dshow" href="#dshow">3.5 dshow</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-4" href="#Options-4">3.5.1 Options</a></li>
      <li><a name="toc-Examples-2" href="#Examples-2">3.5.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-dv1394" href="#dv1394">3.6 dv1394</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-5" href="#Options-5">3.6.1 Options</a></li>
    </ul></li>
    <li><a name="toc-fbdev" href="#fbdev">3.7 fbdev</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-6" href="#Options-6">3.7.1 Options</a></li>
    </ul></li>
    <li><a name="toc-gdigrab" href="#gdigrab">3.8 gdigrab</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-7" href="#Options-7">3.8.1 Options</a></li>
    </ul></li>
    <li><a name="toc-iec61883" href="#iec61883">3.9 iec61883</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-8" href="#Options-8">3.9.1 Options</a></li>
      <li><a name="toc-Examples-3" href="#Examples-3">3.9.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-jack" href="#jack">3.10 jack</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-9" href="#Options-9">3.10.1 Options</a></li>
    </ul></li>
    <li><a name="toc-lavfi" href="#lavfi">3.11 lavfi</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-10" href="#Options-10">3.11.1 Options</a></li>
      <li><a name="toc-Examples-4" href="#Examples-4">3.11.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-libcdio" href="#libcdio">3.12 libcdio</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-11" href="#Options-11">3.12.1 Options</a></li>
    </ul></li>
    <li><a name="toc-libdc1394" href="#libdc1394">3.13 libdc1394</a></li>
    <li><a name="toc-openal" href="#openal">3.14 openal</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-12" href="#Options-12">3.14.1 Options</a></li>
      <li><a name="toc-Examples-5" href="#Examples-5">3.14.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-oss" href="#oss">3.15 oss</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-13" href="#Options-13">3.15.1 Options</a></li>
    </ul></li>
    <li><a name="toc-pulse" href="#pulse">3.16 pulse</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-14" href="#Options-14">3.16.1 Options</a></li>
      <li><a name="toc-Examples-6" href="#Examples-6">3.16.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-qtkit" href="#qtkit">3.17 qtkit</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-15" href="#Options-15">3.17.1 Options</a></li>
    </ul></li>
    <li><a name="toc-sndio" href="#sndio">3.18 sndio</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-16" href="#Options-16">3.18.1 Options</a></li>
    </ul></li>
    <li><a name="toc-video4linux2_002c-v4l2" href="#video4linux2_002c-v4l2">3.19 video4linux2, v4l2</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-17" href="#Options-17">3.19.1 Options</a></li>
    </ul></li>
    <li><a name="toc-vfwcap" href="#vfwcap">3.20 vfwcap</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-18" href="#Options-18">3.20.1 Options</a></li>
    </ul></li>
    <li><a name="toc-x11grab" href="#x11grab">3.21 x11grab</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-19" href="#Options-19">3.21.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-Output-Devices" href="#Output-Devices">4 Output Devices</a>
  <ul class="no-bullet">
    <li><a name="toc-alsa-1" href="#alsa-1">4.1 alsa</a>
    <ul class="no-bullet">
      <li><a name="toc-Examples-7" href="#Examples-7">4.1.1 Examples</a></li>
    </ul></li>
    <li><a name="toc-caca" href="#caca">4.2 caca</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-20" href="#Options-20">4.2.1 Options</a></li>
      <li><a name="toc-Examples-8" href="#Examples-8">4.2.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-decklink-1" href="#decklink-1">4.3 decklink</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-21" href="#Options-21">4.3.1 Options</a></li>
      <li><a name="toc-Examples-9" href="#Examples-9">4.3.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-fbdev-1" href="#fbdev-1">4.4 fbdev</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-22" href="#Options-22">4.4.1 Options</a></li>
      <li><a name="toc-Examples-10" href="#Examples-10">4.4.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-opengl" href="#opengl">4.5 opengl</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-23" href="#Options-23">4.5.1 Options</a></li>
      <li><a name="toc-Examples-11" href="#Examples-11">4.5.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-oss-1" href="#oss-1">4.6 oss</a></li>
    <li><a name="toc-pulse-1" href="#pulse-1">4.7 pulse</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-24" href="#Options-24">4.7.1 Options</a></li>
      <li><a name="toc-Examples-12" href="#Examples-12">4.7.2 Examples</a></li>
    </ul></li>
    <li><a name="toc-sdl" href="#sdl">4.8 sdl</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-25" href="#Options-25">4.8.1 Options</a></li>
      <li><a name="toc-Interactive-commands" href="#Interactive-commands">4.8.2 Interactive commands</a></li>
      <li><a name="toc-Examples-13" href="#Examples-13">4.8.3 Examples</a></li>
    </ul></li>
    <li><a name="toc-sndio-1" href="#sndio-1">4.9 sndio</a></li>
    <li><a name="toc-xv" href="#xv">4.10 xv</a>
    <ul class="no-bullet">
      <li><a name="toc-Options-26" href="#Options-26">4.10.1 Options</a></li>
      <li><a name="toc-Examples-14" href="#Examples-14">4.10.2 Examples</a></li>
    </ul></li>
  </ul></li>
  <li><a name="toc-See-Also" href="#See-Also">5 See Also</a></li>
  <li><a name="toc-Authors" href="#Authors">6 Authors</a></li>
</ul>
</div>


<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes the input and output devices provided by the
libavdevice library.
</p>

<a name="Device-Options"></a>
<h2 class="chapter">2 Device Options<span class="pull-right"><a class="anchor hidden-xs" href="#Device-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Device-Options" aria-hidden="true">TOC</a></span></h2>

<p>The libavdevice library provides the same interface as
libavformat. Namely, an input device is considered like a demuxer, and
an output device like a muxer, and the interface and generic device
options are the same provided by libavformat (see the ffmpeg-formats
manual).
</p>
<p>In addition each input or output device may support so-called private
options, which are specific for that component.
</p>
<p>Options may be set by specifying -<var>option</var> <var>value</var> in the
FFmpeg tools, or by setting the value explicitly in the device
<code>AVFormatContext</code> options or using the <samp>libavutil/opt.h</samp> API
for programmatic use.
</p>

<a name="Input-Devices"></a>
<h2 class="chapter">3 Input Devices<span class="pull-right"><a class="anchor hidden-xs" href="#Input-Devices" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Input-Devices" aria-hidden="true">TOC</a></span></h2>

<p>Input devices are configured elements in FFmpeg which enable accessing
the data coming from a multimedia device attached to your system.
</p>
<p>When you configure your FFmpeg build, all the supported input devices
are enabled by default. You can list all available ones using the
configure option &quot;&ndash;list-indevs&quot;.
</p>
<p>You can disable all the input devices using the configure option
&quot;&ndash;disable-indevs&quot;, and selectively enable an input device using the
option &quot;&ndash;enable-indev=<var>INDEV</var>&quot;, or you can disable a particular
input device using the option &quot;&ndash;disable-indev=<var>INDEV</var>&quot;.
</p>
<p>The option &quot;-devices&quot; of the ff* tools will display the list of
supported input devices.
</p>
<p>A description of the currently available input devices follows.
</p>
<a name="alsa"></a>
<h3 class="section">3.1 alsa<span class="pull-right"><a class="anchor hidden-xs" href="#alsa" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-alsa" aria-hidden="true">TOC</a></span></h3>

<p>ALSA (Advanced Linux Sound Architecture) input device.
</p>
<p>To enable this input device during configuration you need libasound
installed on your system.
</p>
<p>This device allows capturing from an ALSA device. The name of the
device to capture has to be an ALSA card identifier.
</p>
<p>An ALSA identifier has the syntax:
</p><div class="example">
<pre class="example">hw:<var>CARD</var>[,<var>DEV</var>[,<var>SUBDEV</var>]]
</pre></div>

<p>where the <var>DEV</var> and <var>SUBDEV</var> components are optional.
</p>
<p>The three arguments (in order: <var>CARD</var>,<var>DEV</var>,<var>SUBDEV</var>)
specify card number or identifier, device number and subdevice number
(-1 means any).
</p>
<p>To see the list of cards currently recognized by your system check the
files <samp>/proc/asound/cards</samp> and <samp>/proc/asound/devices</samp>.
</p>
<p>For example to capture with <code>ffmpeg</code> from an ALSA device with
card id 0, you may run the command:
</p><div class="example">
<pre class="example">ffmpeg -f alsa -i hw:0 alsaout.wav
</pre></div>

<p>For more information see:
<a href="http://www.alsa-project.org/alsa-doc/alsa-lib/pcm.html">http://www.alsa-project.org/alsa-doc/alsa-lib/pcm.html</a>
</p>
<a name="Options"></a>
<h4 class="subsection">3.1.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>sample_rate</samp></dt>
<dd><p>Set the sample rate in Hz. Default is 48000.
</p>
</dd>
<dt><samp>channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="avfoundation"></a>
<h3 class="section">3.2 avfoundation<span class="pull-right"><a class="anchor hidden-xs" href="#avfoundation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avfoundation" aria-hidden="true">TOC</a></span></h3>

<p>AVFoundation input device.
</p>
<p>AVFoundation is the currently recommended framework by Apple for streamgrabbing on OSX &gt;= 10.7 as well as on iOS.
The older QTKit framework has been marked deprecated since OSX version 10.7.
</p>
<p>The input filename has to be given in the following syntax:
</p><div class="example">
<pre class="example">-i &quot;[[VIDEO]:[AUDIO]]&quot;
</pre></div>
<p>The first entry selects the video input while the latter selects the audio input.
The stream has to be specified by the device name or the device index as shown by the device list.
Alternatively, the video and/or audio input device can be chosen by index using the
<samp>
    -video_device_index &lt;INDEX&gt;
</samp>
and/or
<samp>
    -audio_device_index &lt;INDEX&gt;
</samp>
, overriding any
device name or index given in the input filename.
</p>
<p>All available devices can be enumerated by using <samp>-list_devices true</samp>, listing
all device names and corresponding indices.
</p>
<p>There are two device name aliases:
</p><dl compact="compact">
<dt><code>default</code></dt>
<dd><p>Select the AVFoundation default device of the corresponding type.
</p>
</dd>
<dt><code>none</code></dt>
<dd><p>Do not record the corresponding media type.
This is equivalent to specifying an empty device name or index.
</p>
</dd>
</dl>

<a name="Options-1"></a>
<h4 class="subsection">3.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-1" aria-hidden="true">TOC</a></span></h4>

<p>AVFoundation supports the following options:
</p>
<dl compact="compact">
<dt><samp>-list_devices &lt;TRUE|FALSE&gt;</samp></dt>
<dd><p>If set to true, a list of all available input devices is given showing all
device names and indices.
</p>
</dd>
<dt><samp>-video_device_index &lt;INDEX&gt;</samp></dt>
<dd><p>Specify the video device by its index. Overrides anything given in the input filename.
</p>
</dd>
<dt><samp>-audio_device_index &lt;INDEX&gt;</samp></dt>
<dd><p>Specify the audio device by its index. Overrides anything given in the input filename.
</p>
</dd>
<dt><samp>-pixel_format &lt;FORMAT&gt;</samp></dt>
<dd><p>Request the video device to use a specific pixel format.
If the specified format is not supported, a list of available formats is given
and the first one in this list is used instead. Available pixel formats are:
<code>monob, rgb555be, rgb555le, rgb565be, rgb565le, rgb24, bgr24, 0rgb, bgr0, 0bgr, rgb0,
 bgr48be, uyvy422, yuva444p, yuva444p16le, yuv444p, yuv422p16, yuv422p10, yuv444p10,
 yuv420p, nv12, yuyv422, gray</code>
</p>
</dd>
<dt><samp>-framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default is <code>ntsc</code>, corresponding to a
frame rate of <code>30000/1001</code>.
</p>
</dd>
<dt><samp>-video_size</samp></dt>
<dd><p>Set the video frame size.
</p>
</dd>
<dt><samp>-capture_cursor</samp></dt>
<dd><p>Capture the mouse pointer. Default is 0.
</p>
</dd>
<dt><samp>-capture_mouse_clicks</samp></dt>
<dd><p>Capture the screen mouse clicks. Default is 0.
</p>
</dd>
</dl>

<a name="Examples"></a>
<h4 class="subsection">3.2.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Print the list of AVFoundation supported devices and exit:
<div class="example">
<pre class="example">$ ffmpeg -f avfoundation -list_devices true -i &quot;&quot;
</pre></div>

</li><li> Record video from video device 0 and audio from audio device 0 into out.avi:
<div class="example">
<pre class="example">$ ffmpeg -f avfoundation -i &quot;0:0&quot; out.avi
</pre></div>

</li><li> Record video from video device 2 and audio from audio device 1 into out.avi:
<div class="example">
<pre class="example">$ ffmpeg -f avfoundation -video_device_index 2 -i &quot;:1&quot; out.avi
</pre></div>

</li><li> Record video from the system default video device using the pixel format bgr0 and do not record any audio into out.avi:
<div class="example">
<pre class="example">$ ffmpeg -f avfoundation -pixel_format bgr0 -i &quot;default:none&quot; out.avi
</pre></div>

</li></ul>

<a name="bktr"></a>
<h3 class="section">3.3 bktr<span class="pull-right"><a class="anchor hidden-xs" href="#bktr" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-bktr" aria-hidden="true">TOC</a></span></h3>

<p>BSD video input device.
</p>
<a name="Options-2"></a>
<h4 class="subsection">3.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-2" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>framerate</samp></dt>
<dd><p>Set the frame rate.
</p>
</dd>
<dt><samp>video_size</samp></dt>
<dd><p>Set the video frame size. Default is <code>vga</code>.
</p>
</dd>
<dt><samp>standard</samp></dt>
<dd>
<p>Available values are:
</p><dl compact="compact">
<dt>&lsquo;<samp>pal</samp>&rsquo;</dt>
<dt>&lsquo;<samp>ntsc</samp>&rsquo;</dt>
<dt>&lsquo;<samp>secam</samp>&rsquo;</dt>
<dt>&lsquo;<samp>paln</samp>&rsquo;</dt>
<dt>&lsquo;<samp>palm</samp>&rsquo;</dt>
<dt>&lsquo;<samp>ntscj</samp>&rsquo;</dt>
</dl>

</dd>
</dl>

<a name="decklink"></a>
<h3 class="section">3.4 decklink<span class="pull-right"><a class="anchor hidden-xs" href="#decklink" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-decklink" aria-hidden="true">TOC</a></span></h3>

<p>The decklink input device provides capture capabilities for Blackmagic
DeckLink devices.
</p>
<p>To enable this input device, you need the Blackmagic DeckLink SDK and you
need to configure with the appropriate <code>--extra-cflags</code>
and <code>--extra-ldflags</code>.
On Windows, you need to run the IDL files through <code>widl</code>.
</p>
<p>DeckLink is very picky about the formats it supports. Pixel format is
uyvy422 or v210, framerate and video size must be determined for your device with
<code>-list_formats 1</code>. Audio sample rate is always 48 kHz and the number
of channels can be 2, 8 or 16. Note that all audio channels are bundled in one single
audio track.
</p>
<a name="Options-3"></a>
<h4 class="subsection">3.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-3" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>list_devices</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of devices and exit.
Defaults to <samp>false</samp>.
</p>
</dd>
<dt><samp>list_formats</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of supported formats and exit.
Defaults to <samp>false</samp>.
</p>
</dd>
<dt><samp>bm_v210</samp></dt>
<dd><p>If set to &lsquo;<samp>1</samp>&rsquo;, video is captured in 10 bit v210 instead
of uyvy422. Not all Blackmagic devices support this option.
</p>
</dd>
<dt><samp>teletext_lines</samp></dt>
<dd><p>If set to nonzero, an additional teletext stream will be captured from the
vertical ancillary data. This option is a bitmask of the VBI lines checked,
specifically lines 6 to 22, and lines 318 to 335. Line 6 is the LSB in the mask.
Selected lines which do not contain teletext information will be ignored. You
can use the special <samp>all</samp> constant to select all possible lines, or
<samp>standard</samp> to skip lines 6, 318 and 319, which are not compatible with all
receivers. Capturing teletext only works for SD PAL sources in 8 bit mode.
To use this option, ffmpeg needs to be compiled with <code>--enable-libzvbi</code>.
</p>
</dd>
<dt><samp>channels</samp></dt>
<dd><p>Defines number of audio channels to capture. Must be &lsquo;<samp>2</samp>&rsquo;, &lsquo;<samp>8</samp>&rsquo; or &lsquo;<samp>16</samp>&rsquo;.
Defaults to &lsquo;<samp>2</samp>&rsquo;.
</p>
</dd>
<dt><samp>duplex_mode</samp></dt>
<dd><p>Sets the decklink device duplex mode. Must be &lsquo;<samp>unset</samp>&rsquo;, &lsquo;<samp>half</samp>&rsquo; or &lsquo;<samp>full</samp>&rsquo;.
Defaults to &lsquo;<samp>unset</samp>&rsquo;.
</p>
</dd>
<dt><samp>video_input</samp></dt>
<dd><p>Sets the video input source. Must be &lsquo;<samp>unset</samp>&rsquo;, &lsquo;<samp>sdi</samp>&rsquo;, &lsquo;<samp>hdmi</samp>&rsquo;,
&lsquo;<samp>optical_sdi</samp>&rsquo;, &lsquo;<samp>component</samp>&rsquo;, &lsquo;<samp>composite</samp>&rsquo; or &lsquo;<samp>s_video</samp>&rsquo;.
Defaults to &lsquo;<samp>unset</samp>&rsquo;.
</p>
</dd>
<dt><samp>audio_input</samp></dt>
<dd><p>Sets the audio input source. Must be &lsquo;<samp>unset</samp>&rsquo;, &lsquo;<samp>embedded</samp>&rsquo;,
&lsquo;<samp>aes_ebu</samp>&rsquo;, &lsquo;<samp>analog</samp>&rsquo;, &lsquo;<samp>analog_xlr</samp>&rsquo;, &lsquo;<samp>analog_rca</samp>&rsquo; or
&lsquo;<samp>microphone</samp>&rsquo;. Defaults to &lsquo;<samp>unset</samp>&rsquo;.
</p>
</dd>
<dt><samp>video_pts</samp></dt>
<dd><p>Sets the video packet timestamp source. Must be &lsquo;<samp>video</samp>&rsquo;, &lsquo;<samp>audio</samp>&rsquo;,
&lsquo;<samp>reference</samp>&rsquo; or &lsquo;<samp>wallclock</samp>&rsquo;. Defaults to &lsquo;<samp>video</samp>&rsquo;.
</p>
</dd>
<dt><samp>audio_pts</samp></dt>
<dd><p>Sets the audio packet timestamp source. Must be &lsquo;<samp>video</samp>&rsquo;, &lsquo;<samp>audio</samp>&rsquo;,
&lsquo;<samp>reference</samp>&rsquo; or &lsquo;<samp>wallclock</samp>&rsquo;. Defaults to &lsquo;<samp>audio</samp>&rsquo;.
</p>
</dd>
<dt><samp>draw_bars</samp></dt>
<dd><p>If set to &lsquo;<samp>true</samp>&rsquo;, color bars are drawn in the event of a signal loss.
Defaults to &lsquo;<samp>true</samp>&rsquo;.
</p>
</dd>
</dl>

<a name="Examples-1"></a>
<h4 class="subsection">3.4.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-1" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> List input devices:
<div class="example">
<pre class="example">ffmpeg -f decklink -list_devices 1 -i dummy
</pre></div>

</li><li> List supported formats:
<div class="example">
<pre class="example">ffmpeg -f decklink -list_formats 1 -i 'Intensity Pro'
</pre></div>

</li><li> Capture video clip at 1080i50 (format 11):
<div class="example">
<pre class="example">ffmpeg -f decklink -i 'Intensity Pro@11' -acodec copy -vcodec copy output.avi
</pre></div>

</li><li> Capture video clip at 1080i50 10 bit:
<div class="example">
<pre class="example">ffmpeg -bm_v210 1 -f decklink -i 'UltraStudio Mini Recorder@11' -acodec copy -vcodec copy output.avi
</pre></div>

</li><li> Capture video clip at 1080i50 with 16 audio channels:
<div class="example">
<pre class="example">ffmpeg -channels 16 -f decklink -i 'UltraStudio Mini Recorder@11' -acodec copy -vcodec copy output.avi
</pre></div>

</li></ul>

<a name="dshow"></a>
<h3 class="section">3.5 dshow<span class="pull-right"><a class="anchor hidden-xs" href="#dshow" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dshow" aria-hidden="true">TOC</a></span></h3>

<p>Windows DirectShow input device.
</p>
<p>DirectShow support is enabled when FFmpeg is built with the mingw-w64 project.
Currently only audio and video devices are supported.
</p>
<p>Multiple devices may be opened as separate inputs, but they may also be
opened on the same input, which should improve synchronism between them.
</p>
<p>The input name should be in the format:
</p>
<div class="example">
<pre class="example"><var>TYPE</var>=<var>NAME</var>[:<var>TYPE</var>=<var>NAME</var>]
</pre></div>

<p>where <var>TYPE</var> can be either <var>audio</var> or <var>video</var>,
and <var>NAME</var> is the device&rsquo;s name or alternative name..
</p>
<a name="Options-4"></a>
<h4 class="subsection">3.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-4" aria-hidden="true">TOC</a></span></h4>

<p>If no options are specified, the device&rsquo;s defaults are used.
If the device does not support the requested options, it will
fail to open.
</p>
<dl compact="compact">
<dt><samp>video_size</samp></dt>
<dd><p>Set the video size in the captured video.
</p>
</dd>
<dt><samp>framerate</samp></dt>
<dd><p>Set the frame rate in the captured video.
</p>
</dd>
<dt><samp>sample_rate</samp></dt>
<dd><p>Set the sample rate (in Hz) of the captured audio.
</p>
</dd>
<dt><samp>sample_size</samp></dt>
<dd><p>Set the sample size (in bits) of the captured audio.
</p>
</dd>
<dt><samp>channels</samp></dt>
<dd><p>Set the number of channels in the captured audio.
</p>
</dd>
<dt><samp>list_devices</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of devices and exit.
</p>
</dd>
<dt><samp>list_options</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of selected device&rsquo;s options
and exit.
</p>
</dd>
<dt><samp>video_device_number</samp></dt>
<dd><p>Set video device number for devices with the same name (starts at 0,
defaults to 0).
</p>
</dd>
<dt><samp>audio_device_number</samp></dt>
<dd><p>Set audio device number for devices with the same name (starts at 0,
defaults to 0).
</p>
</dd>
<dt><samp>pixel_format</samp></dt>
<dd><p>Select pixel format to be used by DirectShow. This may only be set when
the video codec is not set or set to rawvideo.
</p>
</dd>
<dt><samp>audio_buffer_size</samp></dt>
<dd><p>Set audio device buffer size in milliseconds (which can directly
impact latency, depending on the device).
Defaults to using the audio device&rsquo;s
default buffer size (typically some multiple of 500ms).
Setting this value too low can degrade performance.
See also
<a href="http://msdn.microsoft.com/en-us/library/windows/desktop/dd377582(v=vs.85).aspx">http://msdn.microsoft.com/en-us/library/windows/desktop/dd377582(v=vs.85).aspx</a>
</p>
</dd>
<dt><samp>video_pin_name</samp></dt>
<dd><p>Select video capture pin to use by name or alternative name.
</p>
</dd>
<dt><samp>audio_pin_name</samp></dt>
<dd><p>Select audio capture pin to use by name or alternative name.
</p>
</dd>
<dt><samp>crossbar_video_input_pin_number</samp></dt>
<dd><p>Select video input pin number for crossbar device. This will be
routed to the crossbar device&rsquo;s Video Decoder output pin.
Note that changing this value can affect future invocations
(sets a new default) until system reboot occurs.
</p>
</dd>
<dt><samp>crossbar_audio_input_pin_number</samp></dt>
<dd><p>Select audio input pin number for crossbar device. This will be
routed to the crossbar device&rsquo;s Audio Decoder output pin.
Note that changing this value can affect future invocations
(sets a new default) until system reboot occurs.
</p>
</dd>
<dt><samp>show_video_device_dialog</samp></dt>
<dd><p>If set to <samp>true</samp>, before capture starts, popup a display dialog
to the end user, allowing them to change video filter properties
and configurations manually.
Note that for crossbar devices, adjusting values in this dialog
may be needed at times to toggle between PAL (25 fps) and NTSC (29.97)
input frame rates, sizes, interlacing, etc.  Changing these values can
enable different scan rates/frame rates and avoiding green bars at
the bottom, flickering scan lines, etc.
Note that with some devices, changing these properties can also affect future
invocations (sets new defaults) until system reboot occurs.
</p>
</dd>
<dt><samp>show_audio_device_dialog</samp></dt>
<dd><p>If set to <samp>true</samp>, before capture starts, popup a display dialog
to the end user, allowing them to change audio filter properties
and configurations manually.
</p>
</dd>
<dt><samp>show_video_crossbar_connection_dialog</samp></dt>
<dd><p>If set to <samp>true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify crossbar pin routings, when it opens a video device.
</p>
</dd>
<dt><samp>show_audio_crossbar_connection_dialog</samp></dt>
<dd><p>If set to <samp>true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify crossbar pin routings, when it opens an audio device.
</p>
</dd>
<dt><samp>show_analog_tv_tuner_dialog</samp></dt>
<dd><p>If set to <samp>true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify TV channels and frequencies.
</p>
</dd>
<dt><samp>show_analog_tv_tuner_audio_dialog</samp></dt>
<dd><p>If set to <samp>true</samp>, before capture starts, popup a display
dialog to the end user, allowing them to manually
modify TV audio (like mono vs. stereo, Language A,B or C).
</p>
</dd>
<dt><samp>audio_device_load</samp></dt>
<dd><p>Load an audio capture filter device from file instead of searching
it by name. It may load additional parameters too, if the filter
supports the serialization of its properties to.
To use this an audio capture source has to be specified, but it can
be anything even fake one.
</p>
</dd>
<dt><samp>audio_device_save</samp></dt>
<dd><p>Save the currently used audio capture filter device and its
parameters (if the filter supports it) to a file.
If a file with the same name exists it will be overwritten.
</p>
</dd>
<dt><samp>video_device_load</samp></dt>
<dd><p>Load a video capture filter device from file instead of searching
it by name. It may load additional parameters too, if the filter
supports the serialization of its properties to.
To use this a video capture source has to be specified, but it can
be anything even fake one.
</p>
</dd>
<dt><samp>video_device_save</samp></dt>
<dd><p>Save the currently used video capture filter device and its
parameters (if the filter supports it) to a file.
If a file with the same name exists it will be overwritten.
</p>
</dd>
</dl>

<a name="Examples-2"></a>
<h4 class="subsection">3.5.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-2" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Print the list of DirectShow supported devices and exit:
<div class="example">
<pre class="example">$ ffmpeg -list_devices true -f dshow -i dummy
</pre></div>

</li><li> Open video device <var>Camera</var>:
<div class="example">
<pre class="example">$ ffmpeg -f dshow -i video=&quot;Camera&quot;
</pre></div>

</li><li> Open second video device with name <var>Camera</var>:
<div class="example">
<pre class="example">$ ffmpeg -f dshow -video_device_number 1 -i video=&quot;Camera&quot;
</pre></div>

</li><li> Open video device <var>Camera</var> and audio device <var>Microphone</var>:
<div class="example">
<pre class="example">$ ffmpeg -f dshow -i video=&quot;Camera&quot;:audio=&quot;Microphone&quot;
</pre></div>

</li><li> Print the list of supported options in selected device and exit:
<div class="example">
<pre class="example">$ ffmpeg -list_options true -f dshow -i video=&quot;Camera&quot;
</pre></div>

</li><li> Specify pin names to capture by name or alternative name, specify alternative device name:
<div class="example">
<pre class="example">$ ffmpeg -f dshow -audio_pin_name &quot;Audio Out&quot; -video_pin_name 2 -i video=video=&quot;@device_pnp_\\?\pci#ven_1a0a&amp;dev_6200&amp;subsys_62021461&amp;rev_01#4&amp;e2c7dd6&amp;0&amp;00e1#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\{ca465100-deb0-4d59-818f-8c477184adf6}&quot;:audio=&quot;Microphone&quot;
</pre></div>

</li><li> Configure a crossbar device, specifying crossbar pins, allow user to adjust video capture properties at startup:
<div class="example">
<pre class="example">$ ffmpeg -f dshow -show_video_device_dialog true -crossbar_video_input_pin_number 0
     -crossbar_audio_input_pin_number 3 -i video=&quot;AVerMedia BDA Analog Capture&quot;:audio=&quot;AVerMedia BDA Analog Capture&quot;
</pre></div>

</li></ul>

<a name="dv1394"></a>
<h3 class="section">3.6 dv1394<span class="pull-right"><a class="anchor hidden-xs" href="#dv1394" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dv1394" aria-hidden="true">TOC</a></span></h3>

<p>Linux DV 1394 input device.
</p>
<a name="Options-5"></a>
<h4 class="subsection">3.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-5" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>framerate</samp></dt>
<dd><p>Set the frame rate. Default is 25.
</p>
</dd>
<dt><samp>standard</samp></dt>
<dd>
<p>Available values are:
</p><dl compact="compact">
<dt>&lsquo;<samp>pal</samp>&rsquo;</dt>
<dt>&lsquo;<samp>ntsc</samp>&rsquo;</dt>
</dl>

<p>Default value is <code>ntsc</code>.
</p>
</dd>
</dl>

<a name="fbdev"></a>
<h3 class="section">3.7 fbdev<span class="pull-right"><a class="anchor hidden-xs" href="#fbdev" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fbdev" aria-hidden="true">TOC</a></span></h3>

<p>Linux framebuffer input device.
</p>
<p>The Linux framebuffer is a graphic hardware-independent abstraction
layer to show graphics on a computer monitor, typically on the
console. It is accessed through a file device node, usually
<samp>/dev/fb0</samp>.
</p>
<p>For more detailed information read the file
Documentation/fb/framebuffer.txt included in the Linux source tree.
</p>
<p>See also <a href="http://linux-fbdev.sourceforge.net/">http://linux-fbdev.sourceforge.net/</a>, and fbset(1).
</p>
<p>To record from the framebuffer device <samp>/dev/fb0</samp> with
<code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -f fbdev -framerate 10 -i /dev/fb0 out.avi
</pre></div>

<p>You can take a single screenshot image with the command:
</p><div class="example">
<pre class="example">ffmpeg -f fbdev -framerate 1 -i /dev/fb0 -frames:v 1 screenshot.jpeg
</pre></div>

<a name="Options-6"></a>
<h4 class="subsection">3.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-6" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>framerate</samp></dt>
<dd><p>Set the frame rate. Default is 25.
</p>
</dd>
</dl>

<a name="gdigrab"></a>
<h3 class="section">3.8 gdigrab<span class="pull-right"><a class="anchor hidden-xs" href="#gdigrab" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gdigrab" aria-hidden="true">TOC</a></span></h3>

<p>Win32 GDI-based screen capture device.
</p>
<p>This device allows you to capture a region of the display on Windows.
</p>
<p>There are two options for the input filename:
</p><div class="example">
<pre class="example">desktop
</pre></div>
<p>or
</p><div class="example">
<pre class="example">title=<var>window_title</var>
</pre></div>

<p>The first option will capture the entire desktop, or a fixed region of the
desktop. The second option will instead capture the contents of a single
window, regardless of its position on the screen.
</p>
<p>For example, to grab the entire desktop using <code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -f gdigrab -framerate 6 -i desktop out.mpg
</pre></div>

<p>Grab a 640x480 region at position <code>10,20</code>:
</p><div class="example">
<pre class="example">ffmpeg -f gdigrab -framerate 6 -offset_x 10 -offset_y 20 -video_size vga -i desktop out.mpg
</pre></div>

<p>Grab the contents of the window named &quot;Calculator&quot;
</p><div class="example">
<pre class="example">ffmpeg -f gdigrab -framerate 6 -i title=Calculator out.mpg
</pre></div>

<a name="Options-7"></a>
<h4 class="subsection">3.8.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-7" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>draw_mouse</samp></dt>
<dd><p>Specify whether to draw the mouse pointer. Use the value <code>0</code> to
not draw the pointer. Default value is <code>1</code>.
</p>
</dd>
<dt><samp>framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default value is <code>ntsc</code>,
corresponding to a frame rate of <code>30000/1001</code>.
</p>
</dd>
<dt><samp>show_region</samp></dt>
<dd><p>Show grabbed region on screen.
</p>
<p>If <var>show_region</var> is specified with <code>1</code>, then the grabbing
region will be indicated on screen. With this option, it is easy to
know what is being grabbed if only a portion of the screen is grabbed.
</p>
<p>Note that <var>show_region</var> is incompatible with grabbing the contents
of a single window.
</p>
<p>For example:
</p><div class="example">
<pre class="example">ffmpeg -f gdigrab -show_region 1 -framerate 6 -video_size cif -offset_x 10 -offset_y 20 -i desktop out.mpg
</pre></div>

</dd>
<dt><samp>video_size</samp></dt>
<dd><p>Set the video frame size. The default is to capture the full screen if <samp>desktop</samp> is selected, or the full window size if <samp>title=<var>window_title</var></samp> is selected.
</p>
</dd>
<dt><samp>offset_x</samp></dt>
<dd><p>When capturing a region with <var>video_size</var>, set the distance from the left edge of the screen or desktop.
</p>
<p>Note that the offset calculation is from the top left corner of the primary monitor on Windows. If you have a monitor positioned to the left of your primary monitor, you will need to use a negative <var>offset_x</var> value to move the region to that monitor.
</p>
</dd>
<dt><samp>offset_y</samp></dt>
<dd><p>When capturing a region with <var>video_size</var>, set the distance from the top edge of the screen or desktop.
</p>
<p>Note that the offset calculation is from the top left corner of the primary monitor on Windows. If you have a monitor positioned above your primary monitor, you will need to use a negative <var>offset_y</var> value to move the region to that monitor.
</p>
</dd>
</dl>

<a name="iec61883"></a>
<h3 class="section">3.9 iec61883<span class="pull-right"><a class="anchor hidden-xs" href="#iec61883" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-iec61883" aria-hidden="true">TOC</a></span></h3>

<p>FireWire DV/HDV input device using libiec61883.
</p>
<p>To enable this input device, you need libiec61883, libraw1394 and
libavc1394 installed on your system. Use the configure option
<code>--enable-libiec61883</code> to compile with the device enabled.
</p>
<p>The iec61883 capture device supports capturing from a video device
connected via IEEE1394 (FireWire), using libiec61883 and the new Linux
FireWire stack (juju). This is the default DV/HDV input method in Linux
Kernel 2.6.37 and later, since the old FireWire stack was removed.
</p>
<p>Specify the FireWire port to be used as input file, or &quot;auto&quot;
to choose the first port connected.
</p>
<a name="Options-8"></a>
<h4 class="subsection">3.9.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-8" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>dvtype</samp></dt>
<dd><p>Override autodetection of DV/HDV. This should only be used if auto
detection does not work, or if usage of a different device type
should be prohibited. Treating a DV device as HDV (or vice versa) will
not work and result in undefined behavior.
The values <samp>auto</samp>, <samp>dv</samp> and <samp>hdv</samp> are supported.
</p>
</dd>
<dt><samp>dvbuffer</samp></dt>
<dd><p>Set maximum size of buffer for incoming data, in frames. For DV, this
is an exact value. For HDV, it is not frame exact, since HDV does
not have a fixed frame size.
</p>
</dd>
<dt><samp>dvguid</samp></dt>
<dd><p>Select the capture device by specifying its GUID. Capturing will only
be performed from the specified device and fails if no device with the
given GUID is found. This is useful to select the input if multiple
devices are connected at the same time.
Look at /sys/bus/firewire/devices to find out the GUIDs.
</p>
</dd>
</dl>

<a name="Examples-3"></a>
<h4 class="subsection">3.9.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-3" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Grab and show the input of a FireWire DV/HDV device.
<div class="example">
<pre class="example">ffplay -f iec61883 -i auto
</pre></div>

</li><li> Grab and record the input of a FireWire DV/HDV device,
using a packet buffer of 100000 packets if the source is HDV.
<div class="example">
<pre class="example">ffmpeg -f iec61883 -i auto -hdvbuffer 100000 out.mpg
</pre></div>

</li></ul>

<a name="jack"></a>
<h3 class="section">3.10 jack<span class="pull-right"><a class="anchor hidden-xs" href="#jack" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-jack" aria-hidden="true">TOC</a></span></h3>

<p>JACK input device.
</p>
<p>To enable this input device during configuration you need libjack
installed on your system.
</p>
<p>A JACK input device creates one or more JACK writable clients, one for
each audio channel, with name <var>client_name</var>:input_<var>N</var>, where
<var>client_name</var> is the name provided by the application, and <var>N</var>
is a number which identifies the channel.
Each writable client will send the acquired data to the FFmpeg input
device.
</p>
<p>Once you have created one or more JACK readable clients, you need to
connect them to one or more JACK writable clients.
</p>
<p>To connect or disconnect JACK clients you can use the <code>jack_connect</code>
and <code>jack_disconnect</code> programs, or do it through a graphical interface,
for example with <code>qjackctl</code>.
</p>
<p>To list the JACK clients and their properties you can invoke the command
<code>jack_lsp</code>.
</p>
<p>Follows an example which shows how to capture a JACK readable client
with <code>ffmpeg</code>.
</p><div class="example">
<pre class="example"># Create a JACK writable client with name &quot;ffmpeg&quot;.
$ ffmpeg -f jack -i ffmpeg -y out.wav

# Start the sample jack_metro readable client.
$ jack_metro -b 120 -d 0.2 -f 4000

# List the current JACK clients.
$ jack_lsp -c
system:capture_1
system:capture_2
system:playback_1
system:playback_2
ffmpeg:input_1
metro:120_bpm

# Connect metro to the ffmpeg writable client.
$ jack_connect metro:120_bpm ffmpeg:input_1
</pre></div>

<p>For more information read:
<a href="http://jackaudio.org/">http://jackaudio.org/</a>
</p>
<a name="Options-9"></a>
<h4 class="subsection">3.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-9" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="lavfi"></a>
<h3 class="section">3.11 lavfi<span class="pull-right"><a class="anchor hidden-xs" href="#lavfi" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-lavfi" aria-hidden="true">TOC</a></span></h3>

<p>Libavfilter input virtual device.
</p>
<p>This input device reads data from the open output pads of a libavfilter
filtergraph.
</p>
<p>For each filtergraph open output, the input device will create a
corresponding stream which is mapped to the generated output. Currently
only video data is supported. The filtergraph is specified through the
option <samp>graph</samp>.
</p>
<a name="Options-10"></a>
<h4 class="subsection">3.11.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-10" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>graph</samp></dt>
<dd><p>Specify the filtergraph to use as input. Each video open output must be
labelled by a unique string of the form &quot;out<var>N</var>&quot;, where <var>N</var> is a
number starting from 0 corresponding to the mapped input stream
generated by the device.
The first unlabelled output is automatically assigned to the &quot;out0&quot;
label, but all the others need to be specified explicitly.
</p>
<p>The suffix &quot;+subcc&quot; can be appended to the output label to create an extra
stream with the closed captions packets attached to that output
(experimental; only for EIA-608 / CEA-708 for now).
The subcc streams are created after all the normal streams, in the order of
the corresponding stream.
For example, if there is &quot;out19+subcc&quot;, &quot;out7+subcc&quot; and up to &quot;out42&quot;, the
stream #43 is subcc for stream #7 and stream #44 is subcc for stream #19.
</p>
<p>If not specified defaults to the filename specified for the input
device.
</p>
</dd>
<dt><samp>graph_file</samp></dt>
<dd><p>Set the filename of the filtergraph to be read and sent to the other
filters. Syntax of the filtergraph is the same as the one specified by
the option <var>graph</var>.
</p>
</dd>
<dt><samp>dumpgraph</samp></dt>
<dd><p>Dump graph to stderr.
</p>
</dd>
</dl>

<a name="Examples-4"></a>
<h4 class="subsection">3.11.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-4" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Create a color video stream and play it back with <code>ffplay</code>:
<div class="example">
<pre class="example">ffplay -f lavfi -graph &quot;color=c=pink [out0]&quot; dummy
</pre></div>

</li><li> As the previous example, but use filename for specifying the graph
description, and omit the &quot;out0&quot; label:
<div class="example">
<pre class="example">ffplay -f lavfi color=c=pink
</pre></div>

</li><li> Create three different video test filtered sources and play them:
<div class="example">
<pre class="example">ffplay -f lavfi -graph &quot;testsrc [out0]; testsrc,hflip [out1]; testsrc,negate [out2]&quot; test3
</pre></div>

</li><li> Read an audio stream from a file using the amovie source and play it
back with <code>ffplay</code>:
<div class="example">
<pre class="example">ffplay -f lavfi &quot;amovie=test.wav&quot;
</pre></div>

</li><li> Read an audio stream and a video stream and play it back with
<code>ffplay</code>:
<div class="example">
<pre class="example">ffplay -f lavfi &quot;movie=test.avi[out0];amovie=test.wav[out1]&quot;
</pre></div>

</li><li> Dump decoded frames to images and closed captions to a file (experimental):
<div class="example">
<pre class="example">ffmpeg -f lavfi -i &quot;movie=test.ts[out0+subcc]&quot; -map v frame%08d.png -map s -c copy -f rawvideo subcc.bin
</pre></div>

</li></ul>

<a name="libcdio"></a>
<h3 class="section">3.12 libcdio<span class="pull-right"><a class="anchor hidden-xs" href="#libcdio" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libcdio" aria-hidden="true">TOC</a></span></h3>

<p>Audio-CD input device based on libcdio.
</p>
<p>To enable this input device during configuration you need libcdio
installed on your system. It requires the configure option
<code>--enable-libcdio</code>.
</p>
<p>This device allows playing and grabbing from an Audio-CD.
</p>
<p>For example to copy with <code>ffmpeg</code> the entire Audio-CD in <samp>/dev/sr0</samp>,
you may run the command:
</p><div class="example">
<pre class="example">ffmpeg -f libcdio -i /dev/sr0 cd.wav
</pre></div>

<a name="Options-11"></a>
<h4 class="subsection">3.12.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-11" aria-hidden="true">TOC</a></span></h4>
<dl compact="compact">
<dt><samp>speed</samp></dt>
<dd><p>Set drive reading speed. Default value is 0.
</p>
<p>The speed is specified CD-ROM speed units. The speed is set through
the libcdio <code>cdio_cddap_speed_set</code> function. On many CD-ROM
drives, specifying a value too large will result in using the fastest
speed.
</p>
</dd>
<dt><samp>paranoia_mode</samp></dt>
<dd><p>Set paranoia recovery mode flags. It accepts one of the following values:
</p>
<dl compact="compact">
<dt>&lsquo;<samp>disable</samp>&rsquo;</dt>
<dt>&lsquo;<samp>verify</samp>&rsquo;</dt>
<dt>&lsquo;<samp>overlap</samp>&rsquo;</dt>
<dt>&lsquo;<samp>neverskip</samp>&rsquo;</dt>
<dt>&lsquo;<samp>full</samp>&rsquo;</dt>
</dl>

<p>Default value is &lsquo;<samp>disable</samp>&rsquo;.
</p>
<p>For more information about the available recovery modes, consult the
paranoia project documentation.
</p></dd>
</dl>

<a name="libdc1394"></a>
<h3 class="section">3.13 libdc1394<span class="pull-right"><a class="anchor hidden-xs" href="#libdc1394" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libdc1394" aria-hidden="true">TOC</a></span></h3>

<p>IIDC1394 input device, based on libdc1394 and libraw1394.
</p>
<p>Requires the configure option <code>--enable-libdc1394</code>.
</p>
<a name="openal"></a>
<h3 class="section">3.14 openal<span class="pull-right"><a class="anchor hidden-xs" href="#openal" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-openal" aria-hidden="true">TOC</a></span></h3>

<p>The OpenAL input device provides audio capture on all systems with a
working OpenAL 1.1 implementation.
</p>
<p>To enable this input device during configuration, you need OpenAL
headers and libraries installed on your system, and need to configure
FFmpeg with <code>--enable-openal</code>.
</p>
<p>OpenAL headers and libraries should be provided as part of your OpenAL
implementation, or as an additional download (an SDK). Depending on your
installation you may need to specify additional flags via the
<code>--extra-cflags</code> and <code>--extra-ldflags</code> for allowing the build
system to locate the OpenAL headers and libraries.
</p>
<p>An incomplete list of OpenAL implementations follows:
</p>
<dl compact="compact">
<dt><strong>Creative</strong></dt>
<dd><p>The official Windows implementation, providing hardware acceleration
with supported devices and software fallback.
See <a href="http://openal.org/">http://openal.org/</a>.
</p></dd>
<dt><strong>OpenAL Soft</strong></dt>
<dd><p>Portable, open source (LGPL) software implementation. Includes
backends for the most common sound APIs on the Windows, Linux,
Solaris, and BSD operating systems.
See <a href="http://kcat.strangesoft.net/openal.html">http://kcat.strangesoft.net/openal.html</a>.
</p></dd>
<dt><strong>Apple</strong></dt>
<dd><p>OpenAL is part of Core Audio, the official Mac OS X Audio interface.
See <a href="http://developer.apple.com/technologies/mac/audio-and-video.html">http://developer.apple.com/technologies/mac/audio-and-video.html</a>
</p></dd>
</dl>

<p>This device allows one to capture from an audio input device handled
through OpenAL.
</p>
<p>You need to specify the name of the device to capture in the provided
filename. If the empty string is provided, the device will
automatically select the default device. You can get the list of the
supported devices by using the option <var>list_devices</var>.
</p>
<a name="Options-12"></a>
<h4 class="subsection">3.14.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-12" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>channels</samp></dt>
<dd><p>Set the number of channels in the captured audio. Only the values
<samp>1</samp> (monaural) and <samp>2</samp> (stereo) are currently supported.
Defaults to <samp>2</samp>.
</p>
</dd>
<dt><samp>sample_size</samp></dt>
<dd><p>Set the sample size (in bits) of the captured audio. Only the values
<samp>8</samp> and <samp>16</samp> are currently supported. Defaults to
<samp>16</samp>.
</p>
</dd>
<dt><samp>sample_rate</samp></dt>
<dd><p>Set the sample rate (in Hz) of the captured audio.
Defaults to <samp>44.1k</samp>.
</p>
</dd>
<dt><samp>list_devices</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of devices and exit.
Defaults to <samp>false</samp>.
</p>
</dd>
</dl>

<a name="Examples-5"></a>
<h4 class="subsection">3.14.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-5" aria-hidden="true">TOC</a></span></h4>

<p>Print the list of OpenAL supported devices and exit:
</p><div class="example">
<pre class="example">$ ffmpeg -list_devices true -f openal -i dummy out.ogg
</pre></div>

<p>Capture from the OpenAL device <samp>DR-BT101 via PulseAudio</samp>:
</p><div class="example">
<pre class="example">$ ffmpeg -f openal -i 'DR-BT101 via PulseAudio' out.ogg
</pre></div>

<p>Capture from the default device (note the empty string &rdquo; as filename):
</p><div class="example">
<pre class="example">$ ffmpeg -f openal -i '' out.ogg
</pre></div>

<p>Capture from two devices simultaneously, writing to two different files,
within the same <code>ffmpeg</code> command:
</p><div class="example">
<pre class="example">$ ffmpeg -f openal -i 'DR-BT101 via PulseAudio' out1.ogg -f openal -i 'ALSA Default' out2.ogg
</pre></div>
<p>Note: not all OpenAL implementations support multiple simultaneous capture -
try the latest OpenAL Soft if the above does not work.
</p>
<a name="oss"></a>
<h3 class="section">3.15 oss<span class="pull-right"><a class="anchor hidden-xs" href="#oss" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-oss" aria-hidden="true">TOC</a></span></h3>

<p>Open Sound System input device.
</p>
<p>The filename to provide to the input device is the device node
representing the OSS input device, and is usually set to
<samp>/dev/dsp</samp>.
</p>
<p>For example to grab from <samp>/dev/dsp</samp> using <code>ffmpeg</code> use the
command:
</p><div class="example">
<pre class="example">ffmpeg -f oss -i /dev/dsp /tmp/oss.wav
</pre></div>

<p>For more information about OSS see:
<a href="http://manuals.opensound.com/usersguide/dsp.html">http://manuals.opensound.com/usersguide/dsp.html</a>
</p>
<a name="Options-13"></a>
<h4 class="subsection">3.15.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-13" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>sample_rate</samp></dt>
<dd><p>Set the sample rate in Hz. Default is 48000.
</p>
</dd>
<dt><samp>channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>


<a name="pulse"></a>
<h3 class="section">3.16 pulse<span class="pull-right"><a class="anchor hidden-xs" href="#pulse" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-pulse" aria-hidden="true">TOC</a></span></h3>

<p>PulseAudio input device.
</p>
<p>To enable this output device you need to configure FFmpeg with <code>--enable-libpulse</code>.
</p>
<p>The filename to provide to the input device is a source device or the
string &quot;default&quot;
</p>
<p>To list the PulseAudio source devices and their properties you can invoke
the command <code>pactl list sources</code>.
</p>
<p>More information about PulseAudio can be found on <a href="http://www.pulseaudio.org">http://www.pulseaudio.org</a>.
</p>
<a name="Options-14"></a>
<h4 class="subsection">3.16.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-14" aria-hidden="true">TOC</a></span></h4>
<dl compact="compact">
<dt><samp>server</samp></dt>
<dd><p>Connect to a specific PulseAudio server, specified by an IP address.
Default server is used when not provided.
</p>
</dd>
<dt><samp>name</samp></dt>
<dd><p>Specify the application name PulseAudio will use when showing active clients,
by default it is the <code>LIBAVFORMAT_IDENT</code> string.
</p>
</dd>
<dt><samp>stream_name</samp></dt>
<dd><p>Specify the stream name PulseAudio will use when showing active streams,
by default it is &quot;record&quot;.
</p>
</dd>
<dt><samp>sample_rate</samp></dt>
<dd><p>Specify the samplerate in Hz, by default 48kHz is used.
</p>
</dd>
<dt><samp>channels</samp></dt>
<dd><p>Specify the channels in use, by default 2 (stereo) is set.
</p>
</dd>
<dt><samp>frame_size</samp></dt>
<dd><p>Specify the number of bytes per frame, by default it is set to 1024.
</p>
</dd>
<dt><samp>fragment_size</samp></dt>
<dd><p>Specify the minimal buffering fragment in PulseAudio, it will affect the
audio latency. By default it is unset.
</p>
</dd>
<dt><samp>wallclock</samp></dt>
<dd><p>Set the initial PTS using the current time. Default is 1.
</p>
</dd>
</dl>

<a name="Examples-6"></a>
<h4 class="subsection">3.16.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-6" aria-hidden="true">TOC</a></span></h4>
<p>Record a stream from default device:
</p><div class="example">
<pre class="example">ffmpeg -f pulse -i default /tmp/pulse.wav
</pre></div>

<a name="qtkit"></a>
<h3 class="section">3.17 qtkit<span class="pull-right"><a class="anchor hidden-xs" href="#qtkit" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-qtkit" aria-hidden="true">TOC</a></span></h3>

<p>QTKit input device.
</p>
<p>The filename passed as input is parsed to contain either a device name or index.
The device index can also be given by using -video_device_index.
A given device index will override any given device name.
If the desired device consists of numbers only, use -video_device_index to identify it.
The default device will be chosen if an empty string  or the device name &quot;default&quot; is given.
The available devices can be enumerated by using -list_devices.
</p>
<div class="example">
<pre class="example">ffmpeg -f qtkit -i &quot;0&quot; out.mpg
</pre></div>

<div class="example">
<pre class="example">ffmpeg -f qtkit -video_device_index 0 -i &quot;&quot; out.mpg
</pre></div>

<div class="example">
<pre class="example">ffmpeg -f qtkit -i &quot;default&quot; out.mpg
</pre></div>

<div class="example">
<pre class="example">ffmpeg -f qtkit -list_devices true -i &quot;&quot;
</pre></div>

<a name="Options-15"></a>
<h4 class="subsection">3.17.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-15" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-15" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>frame_rate</samp></dt>
<dd><p>Set frame rate. Default is 30.
</p>
</dd>
<dt><samp>list_devices</samp></dt>
<dd><p>If set to <code>true</code>, print a list of devices and exit. Default is
<code>false</code>.
</p>
</dd>
<dt><samp>video_device_index</samp></dt>
<dd><p>Select the video device by index for devices with the same name (starts at 0).
</p>
</dd>
</dl>

<a name="sndio"></a>
<h3 class="section">3.18 sndio<span class="pull-right"><a class="anchor hidden-xs" href="#sndio" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sndio" aria-hidden="true">TOC</a></span></h3>

<p>sndio input device.
</p>
<p>To enable this input device during configuration you need libsndio
installed on your system.
</p>
<p>The filename to provide to the input device is the device node
representing the sndio input device, and is usually set to
<samp>/dev/audio0</samp>.
</p>
<p>For example to grab from <samp>/dev/audio0</samp> using <code>ffmpeg</code> use the
command:
</p><div class="example">
<pre class="example">ffmpeg -f sndio -i /dev/audio0 /tmp/oss.wav
</pre></div>

<a name="Options-16"></a>
<h4 class="subsection">3.18.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-16" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-16" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>sample_rate</samp></dt>
<dd><p>Set the sample rate in Hz. Default is 48000.
</p>
</dd>
<dt><samp>channels</samp></dt>
<dd><p>Set the number of channels. Default is 2.
</p>
</dd>
</dl>

<a name="video4linux2_002c-v4l2"></a>
<h3 class="section">3.19 video4linux2, v4l2<span class="pull-right"><a class="anchor hidden-xs" href="#video4linux2_002c-v4l2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-video4linux2_002c-v4l2" aria-hidden="true">TOC</a></span></h3>

<p>Video4Linux2 input video device.
</p>
<p>&quot;v4l2&quot; can be used as alias for &quot;video4linux2&quot;.
</p>
<p>If FFmpeg is built with v4l-utils support (by using the
<code>--enable-libv4l2</code> configure option), it is possible to use it with the
<code>-use_libv4l2</code> input device option.
</p>
<p>The name of the device to grab is a file device node, usually Linux
systems tend to automatically create such nodes when the device
(e.g. an USB webcam) is plugged into the system, and has a name of the
kind <samp>/dev/video<var>N</var></samp>, where <var>N</var> is a number associated to
the device.
</p>
<p>Video4Linux2 devices usually support a limited set of
<var>width</var>x<var>height</var> sizes and frame rates. You can check which are
supported using <code>-list_formats all</code> for Video4Linux2 devices.
Some devices, like TV cards, support one or more standards. It is possible
to list all the supported standards using <code>-list_standards all</code>.
</p>
<p>The time base for the timestamps is 1 microsecond. Depending on the kernel
version and configuration, the timestamps may be derived from the real time
clock (origin at the Unix Epoch) or the monotonic clock (origin usually at
boot time, unaffected by NTP or manual changes to the clock). The
<samp>-timestamps abs</samp> or <samp>-ts abs</samp> option can be used to force
conversion into the real time clock.
</p>
<p>Some usage examples of the video4linux2 device with <code>ffmpeg</code>
and <code>ffplay</code>:
</p><ul>
<li> List supported formats for a video4linux2 device:
<div class="example">
<pre class="example">ffplay -f video4linux2 -list_formats all /dev/video0
</pre></div>

</li><li> Grab and show the input of a video4linux2 device:
<div class="example">
<pre class="example">ffplay -f video4linux2 -framerate 30 -video_size hd720 /dev/video0
</pre></div>

</li><li> Grab and record the input of a video4linux2 device, leave the
frame rate and size as previously set:
<div class="example">
<pre class="example">ffmpeg -f video4linux2 -input_format mjpeg -i /dev/video0 out.mpeg
</pre></div>
</li></ul>

<p>For more information about Video4Linux, check <a href="http://linuxtv.org/">http://linuxtv.org/</a>.
</p>
<a name="Options-17"></a>
<h4 class="subsection">3.19.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-17" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-17" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>standard</samp></dt>
<dd><p>Set the standard. Must be the name of a supported standard. To get a
list of the supported standards, use the <samp>list_standards</samp>
option.
</p>
</dd>
<dt><samp>channel</samp></dt>
<dd><p>Set the input channel number. Default to -1, which means using the
previously selected channel.
</p>
</dd>
<dt><samp>video_size</samp></dt>
<dd><p>Set the video frame size. The argument must be a string in the form
<var>WIDTH</var>x<var>HEIGHT</var> or a valid size abbreviation.
</p>
</dd>
<dt><samp>pixel_format</samp></dt>
<dd><p>Select the pixel format (only valid for raw video input).
</p>
</dd>
<dt><samp>input_format</samp></dt>
<dd><p>Set the preferred pixel format (for raw video) or a codec name.
This option allows one to select the input format, when several are
available.
</p>
</dd>
<dt><samp>framerate</samp></dt>
<dd><p>Set the preferred video frame rate.
</p>
</dd>
<dt><samp>list_formats</samp></dt>
<dd><p>List available formats (supported pixel formats, codecs, and frame
sizes) and exit.
</p>
<p>Available values are:
</p><dl compact="compact">
<dt>&lsquo;<samp>all</samp>&rsquo;</dt>
<dd><p>Show all available (compressed and non-compressed) formats.
</p>
</dd>
<dt>&lsquo;<samp>raw</samp>&rsquo;</dt>
<dd><p>Show only raw video (non-compressed) formats.
</p>
</dd>
<dt>&lsquo;<samp>compressed</samp>&rsquo;</dt>
<dd><p>Show only compressed formats.
</p></dd>
</dl>

</dd>
<dt><samp>list_standards</samp></dt>
<dd><p>List supported standards and exit.
</p>
<p>Available values are:
</p><dl compact="compact">
<dt>&lsquo;<samp>all</samp>&rsquo;</dt>
<dd><p>Show all supported standards.
</p></dd>
</dl>

</dd>
<dt><samp>timestamps, ts</samp></dt>
<dd><p>Set type of timestamps for grabbed frames.
</p>
<p>Available values are:
</p><dl compact="compact">
<dt>&lsquo;<samp>default</samp>&rsquo;</dt>
<dd><p>Use timestamps from the kernel.
</p>
</dd>
<dt>&lsquo;<samp>abs</samp>&rsquo;</dt>
<dd><p>Use absolute timestamps (wall clock).
</p>
</dd>
<dt>&lsquo;<samp>mono2abs</samp>&rsquo;</dt>
<dd><p>Force conversion from monotonic to absolute timestamps.
</p></dd>
</dl>

<p>Default value is <code>default</code>.
</p>
</dd>
<dt><samp>use_libv4l2</samp></dt>
<dd><p>Use libv4l2 (v4l-utils) conversion functions. Default is 0.
</p>
</dd>
</dl>

<a name="vfwcap"></a>
<h3 class="section">3.20 vfwcap<span class="pull-right"><a class="anchor hidden-xs" href="#vfwcap" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vfwcap" aria-hidden="true">TOC</a></span></h3>

<p>VfW (Video for Windows) capture input device.
</p>
<p>The filename passed as input is the capture driver number, ranging from
0 to 9. You may use &quot;list&quot; as filename to print a list of drivers. Any
other filename will be interpreted as device number 0.
</p>
<a name="Options-18"></a>
<h4 class="subsection">3.20.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-18" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-18" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>video_size</samp></dt>
<dd><p>Set the video frame size.
</p>
</dd>
<dt><samp>framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default value is <code>ntsc</code>,
corresponding to a frame rate of <code>30000/1001</code>.
</p>
</dd>
</dl>

<a name="x11grab"></a>
<h3 class="section">3.21 x11grab<span class="pull-right"><a class="anchor hidden-xs" href="#x11grab" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-x11grab" aria-hidden="true">TOC</a></span></h3>

<p>X11 video input device.
</p>
<p>To enable this input device during configuration you need libxcb
installed on your system. It will be automatically detected during
configuration.
</p>
<p>Alternatively, the configure option <samp>--enable-x11grab</samp> exists
for legacy Xlib users.
</p>
<p>This device allows one to capture a region of an X11 display.
</p>
<p>The filename passed as input has the syntax:
</p><div class="example">
<pre class="example">[<var>hostname</var>]:<var>display_number</var>.<var>screen_number</var>[+<var>x_offset</var>,<var>y_offset</var>]
</pre></div>

<p><var>hostname</var>:<var>display_number</var>.<var>screen_number</var> specifies the
X11 display name of the screen to grab from. <var>hostname</var> can be
omitted, and defaults to &quot;localhost&quot;. The environment variable
<code>DISPLAY</code> contains the default display name.
</p>
<p><var>x_offset</var> and <var>y_offset</var> specify the offsets of the grabbed
area with respect to the top-left border of the X11 screen. They
default to 0.
</p>
<p>Check the X11 documentation (e.g. <code>man X</code>) for more detailed
information.
</p>
<p>Use the <code>xdpyinfo</code> program for getting basic information about
the properties of your X11 display (e.g. grep for &quot;name&quot; or
&quot;dimensions&quot;).
</p>
<p>For example to grab from <samp>:0.0</samp> using <code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

<p>Grab at position <code>10,20</code>:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -framerate 25 -video_size cif -i :0.0+10,20 out.mpg
</pre></div>

<a name="Options-19"></a>
<h4 class="subsection">3.21.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-19" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-19" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>draw_mouse</samp></dt>
<dd><p>Specify whether to draw the mouse pointer. A value of <code>0</code> specifies
not to draw the pointer. Default value is <code>1</code>.
</p>
</dd>
<dt><samp>follow_mouse</samp></dt>
<dd><p>Make the grabbed area follow the mouse. The argument can be
<code>centered</code> or a number of pixels <var>PIXELS</var>.
</p>
<p>When it is specified with &quot;centered&quot;, the grabbing region follows the mouse
pointer and keeps the pointer at the center of region; otherwise, the region
follows only when the mouse pointer reaches within <var>PIXELS</var> (greater than
zero) to the edge of region.
</p>
<p>For example:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -follow_mouse centered -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

<p>To follow only when the mouse pointer reaches within 100 pixels to edge:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -follow_mouse 100 -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

</dd>
<dt><samp>framerate</samp></dt>
<dd><p>Set the grabbing frame rate. Default value is <code>ntsc</code>,
corresponding to a frame rate of <code>30000/1001</code>.
</p>
</dd>
<dt><samp>show_region</samp></dt>
<dd><p>Show grabbed region on screen.
</p>
<p>If <var>show_region</var> is specified with <code>1</code>, then the grabbing
region will be indicated on screen. With this option, it is easy to
know what is being grabbed if only a portion of the screen is grabbed.
</p>
</dd>
<dt><samp>region_border</samp></dt>
<dd><p>Set the region border thickness if <samp>-show_region 1</samp> is used.
Range is 1 to 128 and default is 3 (XCB-based x11grab only).
</p>
<p>For example:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -show_region 1 -framerate 25 -video_size cif -i :0.0+10,20 out.mpg
</pre></div>

<p>With <var>follow_mouse</var>:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -follow_mouse centered -show_region 1 -framerate 25 -video_size cif -i :0.0 out.mpg
</pre></div>

</dd>
<dt><samp>video_size</samp></dt>
<dd><p>Set the video frame size. Default value is <code>vga</code>.
</p>
</dd>
<dt><samp>use_shm</samp></dt>
<dd><p>Use the MIT-SHM extension for shared memory. Default value is <code>1</code>.
It may be necessary to disable it for remote displays (legacy x11grab
only).
</p>
</dd>
<dt><samp>grab_x</samp></dt>
<dt><samp>grab_y</samp></dt>
<dd><p>Set the grabbing region coordinates. They are expressed as offset from
the top left corner of the X11 window and correspond to the
<var>x_offset</var> and <var>y_offset</var> parameters in the device name. The
default value for both options is 0.
</p></dd>
</dl>

<a name="Output-Devices"></a>
<h2 class="chapter">4 Output Devices<span class="pull-right"><a class="anchor hidden-xs" href="#Output-Devices" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Output-Devices" aria-hidden="true">TOC</a></span></h2>

<p>Output devices are configured elements in FFmpeg that can write
multimedia data to an output device attached to your system.
</p>
<p>When you configure your FFmpeg build, all the supported output devices
are enabled by default. You can list all available ones using the
configure option &quot;&ndash;list-outdevs&quot;.
</p>
<p>You can disable all the output devices using the configure option
&quot;&ndash;disable-outdevs&quot;, and selectively enable an output device using the
option &quot;&ndash;enable-outdev=<var>OUTDEV</var>&quot;, or you can disable a particular
input device using the option &quot;&ndash;disable-outdev=<var>OUTDEV</var>&quot;.
</p>
<p>The option &quot;-devices&quot; of the ff* tools will display the list of
enabled output devices.
</p>
<p>A description of the currently available output devices follows.
</p>
<a name="alsa-1"></a>
<h3 class="section">4.1 alsa<span class="pull-right"><a class="anchor hidden-xs" href="#alsa-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-alsa-1" aria-hidden="true">TOC</a></span></h3>

<p>ALSA (Advanced Linux Sound Architecture) output device.
</p>
<a name="Examples-7"></a>
<h4 class="subsection">4.1.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-7" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Play a file on default ALSA device:
<div class="example">
<pre class="example">ffmpeg -i INPUT -f alsa default
</pre></div>

</li><li> Play a file on soundcard 1, audio device 7:
<div class="example">
<pre class="example">ffmpeg -i INPUT -f alsa hw:1,7
</pre></div>
</li></ul>

<a name="caca"></a>
<h3 class="section">4.2 caca<span class="pull-right"><a class="anchor hidden-xs" href="#caca" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-caca" aria-hidden="true">TOC</a></span></h3>

<p>CACA output device.
</p>
<p>This output device allows one to show a video stream in CACA window.
Only one CACA window is allowed per application, so you can
have only one instance of this output device in an application.
</p>
<p>To enable this output device you need to configure FFmpeg with
<code>--enable-libcaca</code>.
libcaca is a graphics library that outputs text instead of pixels.
</p>
<p>For more information about libcaca, check:
<a href="http://caca.zoy.org/wiki/libcaca">http://caca.zoy.org/wiki/libcaca</a>
</p>
<a name="Options-20"></a>
<h4 class="subsection">4.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-20" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-20" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>window_title</samp></dt>
<dd><p>Set the CACA window title, if not specified default to the filename
specified for the output device.
</p>
</dd>
<dt><samp>window_size</samp></dt>
<dd><p>Set the CACA window size, can be a string of the form
<var>width</var>x<var>height</var> or a video size abbreviation.
If not specified it defaults to the size of the input video.
</p>
</dd>
<dt><samp>driver</samp></dt>
<dd><p>Set display driver.
</p>
</dd>
<dt><samp>algorithm</samp></dt>
<dd><p>Set dithering algorithm. Dithering is necessary
because the picture being rendered has usually far more colours than
the available palette.
The accepted values are listed with <code>-list_dither algorithms</code>.
</p>
</dd>
<dt><samp>antialias</samp></dt>
<dd><p>Set antialias method. Antialiasing smoothens the rendered
image and avoids the commonly seen staircase effect.
The accepted values are listed with <code>-list_dither antialiases</code>.
</p>
</dd>
<dt><samp>charset</samp></dt>
<dd><p>Set which characters are going to be used when rendering text.
The accepted values are listed with <code>-list_dither charsets</code>.
</p>
</dd>
<dt><samp>color</samp></dt>
<dd><p>Set color to be used when rendering text.
The accepted values are listed with <code>-list_dither colors</code>.
</p>
</dd>
<dt><samp>list_drivers</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of available drivers and exit.
</p>
</dd>
<dt><samp>list_dither</samp></dt>
<dd><p>List available dither options related to the argument.
The argument must be one of <code>algorithms</code>, <code>antialiases</code>,
<code>charsets</code>, <code>colors</code>.
</p></dd>
</dl>

<a name="Examples-8"></a>
<h4 class="subsection">4.2.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-8" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> The following command shows the <code>ffmpeg</code> output is an
CACA window, forcing its size to 80x25:
<div class="example">
<pre class="example">ffmpeg -i INPUT -vcodec rawvideo -pix_fmt rgb24 -window_size 80x25 -f caca -
</pre></div>

</li><li> Show the list of available drivers and exit:
<div class="example">
<pre class="example">ffmpeg -i INPUT -pix_fmt rgb24 -f caca -list_drivers true -
</pre></div>

</li><li> Show the list of available dither colors and exit:
<div class="example">
<pre class="example">ffmpeg -i INPUT -pix_fmt rgb24 -f caca -list_dither colors -
</pre></div>
</li></ul>

<a name="decklink-1"></a>
<h3 class="section">4.3 decklink<span class="pull-right"><a class="anchor hidden-xs" href="#decklink-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-decklink-1" aria-hidden="true">TOC</a></span></h3>

<p>The decklink output device provides playback capabilities for Blackmagic
DeckLink devices.
</p>
<p>To enable this output device, you need the Blackmagic DeckLink SDK and you
need to configure with the appropriate <code>--extra-cflags</code>
and <code>--extra-ldflags</code>.
On Windows, you need to run the IDL files through <code>widl</code>.
</p>
<p>DeckLink is very picky about the formats it supports. Pixel format is always
uyvy422, framerate and video size must be determined for your device with
<code>-list_formats 1</code>. Audio sample rate is always 48 kHz.
</p>
<a name="Options-21"></a>
<h4 class="subsection">4.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-21" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-21" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>list_devices</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of devices and exit.
Defaults to <samp>false</samp>.
</p>
</dd>
<dt><samp>list_formats</samp></dt>
<dd><p>If set to <samp>true</samp>, print a list of supported formats and exit.
Defaults to <samp>false</samp>.
</p>
</dd>
<dt><samp>preroll</samp></dt>
<dd><p>Amount of time to preroll video in seconds.
Defaults to <samp>0.5</samp>.
</p>
</dd>
</dl>

<a name="Examples-9"></a>
<h4 class="subsection">4.3.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-9" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> List output devices:
<div class="example">
<pre class="example">ffmpeg -i test.avi -f decklink -list_devices 1 dummy
</pre></div>

</li><li> List supported formats:
<div class="example">
<pre class="example">ffmpeg -i test.avi -f decklink -list_formats 1 'DeckLink Mini Monitor'
</pre></div>

</li><li> Play video clip:
<div class="example">
<pre class="example">ffmpeg -i test.avi -f decklink -pix_fmt uyvy422 'DeckLink Mini Monitor'
</pre></div>

</li><li> Play video clip with non-standard framerate or video size:
<div class="example">
<pre class="example">ffmpeg -i test.avi -f decklink -pix_fmt uyvy422 -s 720x486 -r 24000/1001 'DeckLink Mini Monitor'
</pre></div>

</li></ul>

<a name="fbdev-1"></a>
<h3 class="section">4.4 fbdev<span class="pull-right"><a class="anchor hidden-xs" href="#fbdev-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fbdev-1" aria-hidden="true">TOC</a></span></h3>

<p>Linux framebuffer output device.
</p>
<p>The Linux framebuffer is a graphic hardware-independent abstraction
layer to show graphics on a computer monitor, typically on the
console. It is accessed through a file device node, usually
<samp>/dev/fb0</samp>.
</p>
<p>For more detailed information read the file
<samp>Documentation/fb/framebuffer.txt</samp> included in the Linux source tree.
</p>
<a name="Options-22"></a>
<h4 class="subsection">4.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-22" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-22" aria-hidden="true">TOC</a></span></h4>
<dl compact="compact">
<dt><samp>xoffset</samp></dt>
<dt><samp>yoffset</samp></dt>
<dd><p>Set x/y coordinate of top left corner. Default is 0.
</p></dd>
</dl>

<a name="Examples-10"></a>
<h4 class="subsection">4.4.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-10" aria-hidden="true">TOC</a></span></h4>
<p>Play a file on framebuffer device <samp>/dev/fb0</samp>.
Required pixel format depends on current framebuffer settings.
</p><div class="example">
<pre class="example">ffmpeg -re -i INPUT -vcodec rawvideo -pix_fmt bgra -f fbdev /dev/fb0
</pre></div>

<p>See also <a href="http://linux-fbdev.sourceforge.net/">http://linux-fbdev.sourceforge.net/</a>, and fbset(1).
</p>
<a name="opengl"></a>
<h3 class="section">4.5 opengl<span class="pull-right"><a class="anchor hidden-xs" href="#opengl" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-opengl" aria-hidden="true">TOC</a></span></h3>
<p>OpenGL output device.
</p>
<p>To enable this output device you need to configure FFmpeg with <code>--enable-opengl</code>.
</p>
<p>This output device allows one to render to OpenGL context.
Context may be provided by application or default SDL window is created.
</p>
<p>When device renders to external context, application must implement handlers for following messages:
<code>AV_DEV_TO_APP_CREATE_WINDOW_BUFFER</code> - create OpenGL context on current thread.
<code>AV_DEV_TO_APP_PREPARE_WINDOW_BUFFER</code> - make OpenGL context current.
<code>AV_DEV_TO_APP_DISPLAY_WINDOW_BUFFER</code> - swap buffers.
<code>AV_DEV_TO_APP_DESTROY_WINDOW_BUFFER</code> - destroy OpenGL context.
Application is also required to inform a device about current resolution by sending <code>AV_APP_TO_DEV_WINDOW_SIZE</code> message.
</p>
<a name="Options-23"></a>
<h4 class="subsection">4.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-23" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-23" aria-hidden="true">TOC</a></span></h4>
<dl compact="compact">
<dt><samp>background</samp></dt>
<dd><p>Set background color. Black is a default.
</p></dd>
<dt><samp>no_window</samp></dt>
<dd><p>Disables default SDL window when set to non-zero value.
Application must provide OpenGL context and both <code>window_size_cb</code> and <code>window_swap_buffers_cb</code> callbacks when set.
</p></dd>
<dt><samp>window_title</samp></dt>
<dd><p>Set the SDL window title, if not specified default to the filename specified for the output device.
Ignored when <samp>no_window</samp> is set.
</p></dd>
<dt><samp>window_size</samp></dt>
<dd><p>Set preferred window size, can be a string of the form widthxheight or a video size abbreviation.
If not specified it defaults to the size of the input video, downscaled according to the aspect ratio.
Mostly usable when <samp>no_window</samp> is not set.
</p>
</dd>
</dl>

<a name="Examples-11"></a>
<h4 class="subsection">4.5.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-11" aria-hidden="true">TOC</a></span></h4>
<p>Play a file on SDL window using OpenGL rendering:
</p><div class="example">
<pre class="example">ffmpeg  -i INPUT -f opengl &quot;window title&quot;
</pre></div>

<a name="oss-1"></a>
<h3 class="section">4.6 oss<span class="pull-right"><a class="anchor hidden-xs" href="#oss-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-oss-1" aria-hidden="true">TOC</a></span></h3>

<p>OSS (Open Sound System) output device.
</p>
<a name="pulse-1"></a>
<h3 class="section">4.7 pulse<span class="pull-right"><a class="anchor hidden-xs" href="#pulse-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-pulse-1" aria-hidden="true">TOC</a></span></h3>

<p>PulseAudio output device.
</p>
<p>To enable this output device you need to configure FFmpeg with <code>--enable-libpulse</code>.
</p>
<p>More information about PulseAudio can be found on <a href="http://www.pulseaudio.org">http://www.pulseaudio.org</a>
</p>
<a name="Options-24"></a>
<h4 class="subsection">4.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-24" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-24" aria-hidden="true">TOC</a></span></h4>
<dl compact="compact">
<dt><samp>server</samp></dt>
<dd><p>Connect to a specific PulseAudio server, specified by an IP address.
Default server is used when not provided.
</p>
</dd>
<dt><samp>name</samp></dt>
<dd><p>Specify the application name PulseAudio will use when showing active clients,
by default it is the <code>LIBAVFORMAT_IDENT</code> string.
</p>
</dd>
<dt><samp>stream_name</samp></dt>
<dd><p>Specify the stream name PulseAudio will use when showing active streams,
by default it is set to the specified output name.
</p>
</dd>
<dt><samp>device</samp></dt>
<dd><p>Specify the device to use. Default device is used when not provided.
List of output devices can be obtained with command <code>pactl list sinks</code>.
</p>
</dd>
<dt><samp>buffer_size</samp></dt>
<dt><samp>buffer_duration</samp></dt>
<dd><p>Control the size and duration of the PulseAudio buffer. A small buffer
gives more control, but requires more frequent updates.
</p>
<p><samp>buffer_size</samp> specifies size in bytes while
<samp>buffer_duration</samp> specifies duration in milliseconds.
</p>
<p>When both options are provided then the highest value is used
(duration is recalculated to bytes using stream parameters). If they
are set to 0 (which is default), the device will use the default
PulseAudio duration value. By default PulseAudio set buffer duration
to around 2 seconds.
</p>
</dd>
<dt><samp>prebuf</samp></dt>
<dd><p>Specify pre-buffering size in bytes. The server does not start with
playback before at least <samp>prebuf</samp> bytes are available in the
buffer. By default this option is initialized to the same value as
<samp>buffer_size</samp> or <samp>buffer_duration</samp> (whichever is bigger).
</p>
</dd>
<dt><samp>minreq</samp></dt>
<dd><p>Specify minimum request size in bytes. The server does not request less
than <samp>minreq</samp> bytes from the client, instead waits until the buffer
is free enough to request more bytes at once. It is recommended to not set
this option, which will initialize this to a value that is deemed sensible
by the server.
</p>
</dd>
</dl>

<a name="Examples-12"></a>
<h4 class="subsection">4.7.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-12" aria-hidden="true">TOC</a></span></h4>
<p>Play a file on default device on default server:
</p><div class="example">
<pre class="example">ffmpeg  -i INPUT -f pulse &quot;stream name&quot;
</pre></div>

<a name="sdl"></a>
<h3 class="section">4.8 sdl<span class="pull-right"><a class="anchor hidden-xs" href="#sdl" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sdl" aria-hidden="true">TOC</a></span></h3>

<p>SDL (Simple DirectMedia Layer) output device.
</p>
<p>This output device allows one to show a video stream in an SDL
window. Only one SDL window is allowed per application, so you can
have only one instance of this output device in an application.
</p>
<p>To enable this output device you need libsdl installed on your system
when configuring your build.
</p>
<p>For more information about SDL, check:
<a href="http://www.libsdl.org/">http://www.libsdl.org/</a>
</p>
<a name="Options-25"></a>
<h4 class="subsection">4.8.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-25" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-25" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>window_title</samp></dt>
<dd><p>Set the SDL window title, if not specified default to the filename
specified for the output device.
</p>
</dd>
<dt><samp>icon_title</samp></dt>
<dd><p>Set the name of the iconified SDL window, if not specified it is set
to the same value of <var>window_title</var>.
</p>
</dd>
<dt><samp>window_size</samp></dt>
<dd><p>Set the SDL window size, can be a string of the form
<var>width</var>x<var>height</var> or a video size abbreviation.
If not specified it defaults to the size of the input video,
downscaled according to the aspect ratio.
</p>
</dd>
<dt><samp>window_fullscreen</samp></dt>
<dd><p>Set fullscreen mode when non-zero value is provided.
Default value is zero.
</p></dd>
</dl>

<a name="Interactive-commands"></a>
<h4 class="subsection">4.8.2 Interactive commands<span class="pull-right"><a class="anchor hidden-xs" href="#Interactive-commands" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Interactive-commands" aria-hidden="true">TOC</a></span></h4>

<p>The window created by the device can be controlled through the
following interactive commands.
</p>
<dl compact="compact">
<dt><tt class="key">q, ESC</tt></dt>
<dd><p>Quit the device immediately.
</p></dd>
</dl>

<a name="Examples-13"></a>
<h4 class="subsection">4.8.3 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-13" aria-hidden="true">TOC</a></span></h4>

<p>The following command shows the <code>ffmpeg</code> output is an
SDL window, forcing its size to the qcif format:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -vcodec rawvideo -pix_fmt yuv420p -window_size qcif -f sdl &quot;SDL output&quot;
</pre></div>

<a name="sndio-1"></a>
<h3 class="section">4.9 sndio<span class="pull-right"><a class="anchor hidden-xs" href="#sndio-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sndio-1" aria-hidden="true">TOC</a></span></h3>

<p>sndio audio output device.
</p>
<a name="xv"></a>
<h3 class="section">4.10 xv<span class="pull-right"><a class="anchor hidden-xs" href="#xv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-xv" aria-hidden="true">TOC</a></span></h3>

<p>XV (XVideo) output device.
</p>
<p>This output device allows one to show a video stream in a X Window System
window.
</p>
<a name="Options-26"></a>
<h4 class="subsection">4.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-26" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-26" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><samp>display_name</samp></dt>
<dd><p>Specify the hardware display name, which determines the display and
communications domain to be used.
</p>
<p>The display name or DISPLAY environment variable can be a string in
the format <var>hostname</var>[:<var>number</var>[.<var>screen_number</var>]].
</p>
<p><var>hostname</var> specifies the name of the host machine on which the
display is physically attached. <var>number</var> specifies the number of
the display server on that host machine. <var>screen_number</var> specifies
the screen to be used on that server.
</p>
<p>If unspecified, it defaults to the value of the DISPLAY environment
variable.
</p>
<p>For example, <code>dual-headed:0.1</code> would specify screen 1 of display
0 on the machine named &ldquo;dual-headed&rdquo;.
</p>
<p>Check the X11 specification for more detailed information about the
display name format.
</p>
</dd>
<dt><samp>window_id</samp></dt>
<dd><p>When set to non-zero value then device doesn&rsquo;t create new window,
but uses existing one with provided <var>window_id</var>. By default
this options is set to zero and device creates its own window.
</p>
</dd>
<dt><samp>window_size</samp></dt>
<dd><p>Set the created window size, can be a string of the form
<var>width</var>x<var>height</var> or a video size abbreviation. If not
specified it defaults to the size of the input video.
Ignored when <var>window_id</var> is set.
</p>
</dd>
<dt><samp>window_x</samp></dt>
<dt><samp>window_y</samp></dt>
<dd><p>Set the X and Y window offsets for the created window. They are both
set to 0 by default. The values may be ignored by the window manager.
Ignored when <var>window_id</var> is set.
</p>
</dd>
<dt><samp>window_title</samp></dt>
<dd><p>Set the window title, if not specified default to the filename
specified for the output device. Ignored when <var>window_id</var> is set.
</p></dd>
</dl>

<p>For more information about XVideo see <a href="http://www.x.org/">http://www.x.org/</a>.
</p>
<a name="Examples-14"></a>
<h4 class="subsection">4.10.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-14" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Decode, display and encode video input with <code>ffmpeg</code> at the
same time:
<div class="example">
<pre class="example">ffmpeg -i INPUT OUTPUT -f xv display
</pre></div>

</li><li> Decode and display the input video to multiple X11 windows:
<div class="example">
<pre class="example">ffmpeg -i INPUT -f xv normal -vf negate -f xv negated
</pre></div>
</li></ul>


<a name="See-Also"></a>
<h2 class="chapter">5 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffmpeg.html">ffmpeg</a>, <a href="ffplay.html">ffplay</a>, <a href="ffprobe.html">ffprobe</a>, <a href="ffserver.html">ffserver</a>,
<a href="libavdevice.html">libavdevice</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">6 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(git://source.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="http://source.ffmpeg.org">http://source.ffmpeg.org</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>


      <p style="font-size: small;">
        This document was generated using <a href="http://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
