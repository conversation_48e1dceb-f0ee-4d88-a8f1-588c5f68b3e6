<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 5.2, http://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      ffplay Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      ffplay Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<a name="SEC_Contents"></a>
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a name="toc-Synopsis" href="#Synopsis">1 Synopsis</a></li>
  <li><a name="toc-Description" href="#Description">2 Description</a></li>
  <li><a name="toc-Options" href="#Options">3 Options</a>
  <ul class="no-bullet">
    <li><a name="toc-Stream-specifiers-1" href="#Stream-specifiers-1">3.1 Stream specifiers</a></li>
    <li><a name="toc-Generic-options" href="#Generic-options">3.2 Generic options</a></li>
    <li><a name="toc-AVOptions" href="#AVOptions">3.3 AVOptions</a></li>
    <li><a name="toc-Main-options" href="#Main-options">3.4 Main options</a></li>
    <li><a name="toc-Advanced-options" href="#Advanced-options">3.5 Advanced options</a></li>
    <li><a name="toc-While-playing" href="#While-playing">3.6 While playing</a></li>
  </ul></li>
  <li><a name="toc-See-Also" href="#See-Also">4 See Also</a></li>
  <li><a name="toc-Authors" href="#Authors">5 Authors</a></li>
</ul>
</div>


<a name="Synopsis"></a>
<h2 class="chapter">1 Synopsis<span class="pull-right"><a class="anchor hidden-xs" href="#Synopsis" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Synopsis" aria-hidden="true">TOC</a></span></h2>

<p>ffplay [<var>options</var>] [<samp>input_file</samp>]
</p>
<a name="Description"></a>
<h2 class="chapter">2 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>FFplay is a very simple and portable media player using the FFmpeg
libraries and the SDL library. It is mostly used as a testbed for the
various FFmpeg APIs.
</p>
<a name="Options"></a>
<h2 class="chapter">3 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options" aria-hidden="true">TOC</a></span></h2>

<p>All the numerical options, if not specified otherwise, accept a string
representing a number as input, which may be followed by one of the SI
unit prefixes, for example: &rsquo;K&rsquo;, &rsquo;M&rsquo;, or &rsquo;G&rsquo;.
</p>
<p>If &rsquo;i&rsquo; is appended to the SI unit prefix, the complete prefix will be
interpreted as a unit prefix for binary multiples, which are based on
powers of 1024 instead of powers of 1000. Appending &rsquo;B&rsquo; to the SI unit
prefix multiplies the value by 8. This allows using, for example:
&rsquo;KB&rsquo;, &rsquo;MiB&rsquo;, &rsquo;G&rsquo; and &rsquo;B&rsquo; as number suffixes.
</p>
<p>Options which do not take arguments are boolean options, and set the
corresponding value to true. They can be set to false by prefixing
the option name with &quot;no&quot;. For example using &quot;-nofoo&quot;
will set the boolean option with name &quot;foo&quot; to false.
</p>
<a name="Stream-specifiers"></a><a name="Stream-specifiers-1"></a>
<h3 class="section">3.1 Stream specifiers<span class="pull-right"><a class="anchor hidden-xs" href="#Stream-specifiers-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Stream-specifiers-1" aria-hidden="true">TOC</a></span></h3>
<p>Some options are applied per-stream, e.g. bitrate or codec. Stream specifiers
are used to precisely specify which stream(s) a given option belongs to.
</p>
<p>A stream specifier is a string generally appended to the option name and
separated from it by a colon. E.g. <code>-codec:a:1 ac3</code> contains the
<code>a:1</code> stream specifier, which matches the second audio stream. Therefore, it
would select the ac3 codec for the second audio stream.
</p>
<p>A stream specifier can match several streams, so that the option is applied to all
of them. E.g. the stream specifier in <code>-b:a 128k</code> matches all audio
streams.
</p>
<p>An empty stream specifier matches all streams. For example, <code>-codec copy</code>
or <code>-codec: copy</code> would copy all the streams without reencoding.
</p>
<p>Possible forms of stream specifiers are:
</p><dl compact="compact">
<dt><samp><var>stream_index</var></samp></dt>
<dd><p>Matches the stream with this index. E.g. <code>-threads:1 4</code> would set the
thread count for the second stream to 4.
</p></dd>
<dt><samp><var>stream_type</var>[:<var>stream_index</var>]</samp></dt>
<dd><p><var>stream_type</var> is one of following: &rsquo;v&rsquo; or &rsquo;V&rsquo; for video, &rsquo;a&rsquo; for audio, &rsquo;s&rsquo;
for subtitle, &rsquo;d&rsquo; for data, and &rsquo;t&rsquo; for attachments. &rsquo;v&rsquo; matches all video
streams, &rsquo;V&rsquo; only matches video streams which are not attached pictures, video
thumbnails or cover arts.  If <var>stream_index</var> is given, then it matches
stream number <var>stream_index</var> of this type. Otherwise, it matches all
streams of this type.
</p></dd>
<dt><samp>p:<var>program_id</var>[:<var>stream_index</var>]</samp></dt>
<dd><p>If <var>stream_index</var> is given, then it matches the stream with number <var>stream_index</var>
in the program with the id <var>program_id</var>. Otherwise, it matches all streams in the
program.
</p></dd>
<dt><samp>#<var>stream_id</var> or i:<var>stream_id</var></samp></dt>
<dd><p>Match the stream by stream id (e.g. PID in MPEG-TS container).
</p></dd>
<dt><samp>m:<var>key</var>[:<var>value</var>]</samp></dt>
<dd><p>Matches streams with the metadata tag <var>key</var> having the specified value. If
<var>value</var> is not given, matches streams that contain the given tag with any
value.
</p></dd>
<dt><samp>u</samp></dt>
<dd><p>Matches streams with usable configuration, the codec must be defined and the
essential information such as video dimension or audio sample rate must be present.
</p>
<p>Note that in <code>ffmpeg</code>, matching by metadata will only work properly for
input files.
</p></dd>
</dl>

<a name="Generic-options"></a>
<h3 class="section">3.2 Generic options<span class="pull-right"><a class="anchor hidden-xs" href="#Generic-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Generic-options" aria-hidden="true">TOC</a></span></h3>

<p>These options are shared amongst the ff* tools.
</p>
<dl compact="compact">
<dt><samp>-L</samp></dt>
<dd><p>Show license.
</p>
</dd>
<dt><samp>-h, -?, -help, --help [<var>arg</var>]</samp></dt>
<dd><p>Show help. An optional parameter may be specified to print help about a specific
item. If no argument is specified, only basic (non advanced) tool
options are shown.
</p>
<p>Possible values of <var>arg</var> are:
</p><dl compact="compact">
<dt><samp>long</samp></dt>
<dd><p>Print advanced tool options in addition to the basic tool options.
</p>
</dd>
<dt><samp>full</samp></dt>
<dd><p>Print complete list of options, including shared and private options
for encoders, decoders, demuxers, muxers, filters, etc.
</p>
</dd>
<dt><samp>decoder=<var>decoder_name</var></samp></dt>
<dd><p>Print detailed information about the decoder named <var>decoder_name</var>. Use the
<samp>-decoders</samp> option to get a list of all decoders.
</p>
</dd>
<dt><samp>encoder=<var>encoder_name</var></samp></dt>
<dd><p>Print detailed information about the encoder named <var>encoder_name</var>. Use the
<samp>-encoders</samp> option to get a list of all encoders.
</p>
</dd>
<dt><samp>demuxer=<var>demuxer_name</var></samp></dt>
<dd><p>Print detailed information about the demuxer named <var>demuxer_name</var>. Use the
<samp>-formats</samp> option to get a list of all demuxers and muxers.
</p>
</dd>
<dt><samp>muxer=<var>muxer_name</var></samp></dt>
<dd><p>Print detailed information about the muxer named <var>muxer_name</var>. Use the
<samp>-formats</samp> option to get a list of all muxers and demuxers.
</p>
</dd>
<dt><samp>filter=<var>filter_name</var></samp></dt>
<dd><p>Print detailed information about the filter name <var>filter_name</var>. Use the
<samp>-filters</samp> option to get a list of all filters.
</p></dd>
</dl>

</dd>
<dt><samp>-version</samp></dt>
<dd><p>Show version.
</p>
</dd>
<dt><samp>-formats</samp></dt>
<dd><p>Show available formats (including devices).
</p>
</dd>
<dt><samp>-devices</samp></dt>
<dd><p>Show available devices.
</p>
</dd>
<dt><samp>-codecs</samp></dt>
<dd><p>Show all codecs known to libavcodec.
</p>
<p>Note that the term &rsquo;codec&rsquo; is used throughout this documentation as a shortcut
for what is more correctly called a media bitstream format.
</p>
</dd>
<dt><samp>-decoders</samp></dt>
<dd><p>Show available decoders.
</p>
</dd>
<dt><samp>-encoders</samp></dt>
<dd><p>Show all available encoders.
</p>
</dd>
<dt><samp>-bsfs</samp></dt>
<dd><p>Show available bitstream filters.
</p>
</dd>
<dt><samp>-protocols</samp></dt>
<dd><p>Show available protocols.
</p>
</dd>
<dt><samp>-filters</samp></dt>
<dd><p>Show available libavfilter filters.
</p>
</dd>
<dt><samp>-pix_fmts</samp></dt>
<dd><p>Show available pixel formats.
</p>
</dd>
<dt><samp>-sample_fmts</samp></dt>
<dd><p>Show available sample formats.
</p>
</dd>
<dt><samp>-layouts</samp></dt>
<dd><p>Show channel names and standard channel layouts.
</p>
</dd>
<dt><samp>-colors</samp></dt>
<dd><p>Show recognized color names.
</p>
</dd>
<dt><samp>-sources <var>device</var>[,<var>opt1</var>=<var>val1</var>[,<var>opt2</var>=<var>val2</var>]...]</samp></dt>
<dd><p>Show autodetected sources of the intput device.
Some devices may provide system-dependent source names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
</p><div class="example">
<pre class="example">ffmpeg -sources pulse,server=***********
</pre></div>

</dd>
<dt><samp>-sinks <var>device</var>[,<var>opt1</var>=<var>val1</var>[,<var>opt2</var>=<var>val2</var>]...]</samp></dt>
<dd><p>Show autodetected sinks of the output device.
Some devices may provide system-dependent sink names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
</p><div class="example">
<pre class="example">ffmpeg -sinks pulse,server=***********
</pre></div>

</dd>
<dt><samp>-loglevel [repeat+]<var>loglevel</var> | -v [repeat+]<var>loglevel</var></samp></dt>
<dd><p>Set the logging level used by the library.
Adding &quot;repeat+&quot; indicates that repeated log output should not be compressed
to the first line and the &quot;Last message repeated n times&quot; line will be
omitted. &quot;repeat&quot; can also be used alone.
If &quot;repeat&quot; is used alone, and with no prior loglevel set, the default
loglevel will be used. If multiple loglevel parameters are given, using
&rsquo;repeat&rsquo; will not change the loglevel.
<var>loglevel</var> is a string or a number containing one of the following values:
</p><dl compact="compact">
<dt>&lsquo;<samp>quiet, -8</samp>&rsquo;</dt>
<dd><p>Show nothing at all; be silent.
</p></dd>
<dt>&lsquo;<samp>panic, 0</samp>&rsquo;</dt>
<dd><p>Only show fatal errors which could lead the process to crash, such as
an assertion failure. This is not currently used for anything.
</p></dd>
<dt>&lsquo;<samp>fatal, 8</samp>&rsquo;</dt>
<dd><p>Only show fatal errors. These are errors after which the process absolutely
cannot continue.
</p></dd>
<dt>&lsquo;<samp>error, 16</samp>&rsquo;</dt>
<dd><p>Show all errors, including ones which can be recovered from.
</p></dd>
<dt>&lsquo;<samp>warning, 24</samp>&rsquo;</dt>
<dd><p>Show all warnings and errors. Any message related to possibly
incorrect or unexpected events will be shown.
</p></dd>
<dt>&lsquo;<samp>info, 32</samp>&rsquo;</dt>
<dd><p>Show informative messages during processing. This is in addition to
warnings and errors. This is the default value.
</p></dd>
<dt>&lsquo;<samp>verbose, 40</samp>&rsquo;</dt>
<dd><p>Same as <code>info</code>, except more verbose.
</p></dd>
<dt>&lsquo;<samp>debug, 48</samp>&rsquo;</dt>
<dd><p>Show everything, including debugging information.
</p></dd>
<dt>&lsquo;<samp>trace, 56</samp>&rsquo;</dt>
</dl>

<p>By default the program logs to stderr. If coloring is supported by the
terminal, colors are used to mark errors and warnings. Log coloring
can be disabled setting the environment variable
<code>AV_LOG_FORCE_NOCOLOR</code> or <code>NO_COLOR</code>, or can be forced setting
the environment variable <code>AV_LOG_FORCE_COLOR</code>.
The use of the environment variable <code>NO_COLOR</code> is deprecated and
will be dropped in a future FFmpeg version.
</p>
</dd>
<dt><samp>-report</samp></dt>
<dd><p>Dump full command line and console output to a file named
<code><var>program</var>-<var>YYYYMMDD</var>-<var>HHMMSS</var>.log</code> in the current
directory.
This file can be useful for bug reports.
It also implies <code>-loglevel verbose</code>.
</p>
<p>Setting the environment variable <code>FFREPORT</code> to any value has the
same effect. If the value is a &rsquo;:&rsquo;-separated key=value sequence, these
options will affect the report; option values must be escaped if they
contain special characters or the options delimiter &rsquo;:&rsquo; (see the
&ldquo;Quoting and escaping&rdquo; section in the ffmpeg-utils manual).
</p>
<p>The following options are recognized:
</p><dl compact="compact">
<dt><samp>file</samp></dt>
<dd><p>set the file name to use for the report; <code>%p</code> is expanded to the name
of the program, <code>%t</code> is expanded to a timestamp, <code>%%</code> is expanded
to a plain <code>%</code>
</p></dd>
<dt><samp>level</samp></dt>
<dd><p>set the log verbosity level using a numerical value (see <code>-loglevel</code>).
</p></dd>
</dl>

<p>For example, to output a report to a file named <samp>ffreport.log</samp>
using a log level of <code>32</code> (alias for log level <code>info</code>):
</p>
<div class="example">
<pre class="example">FFREPORT=file=ffreport.log:level=32 ffmpeg -i input output
</pre></div>

<p>Errors in parsing the environment variable are not fatal, and will not
appear in the report.
</p>
</dd>
<dt><samp>-hide_banner</samp></dt>
<dd><p>Suppress printing banner.
</p>
<p>All FFmpeg tools will normally show a copyright notice, build options
and library versions. This option can be used to suppress printing
this information.
</p>
</dd>
<dt><samp>-cpuflags flags (<em>global</em>)</samp></dt>
<dd><p>Allows setting and clearing cpu flags. This option is intended
for testing. Do not use it unless you know what you&rsquo;re doing.
</p><div class="example">
<pre class="example">ffmpeg -cpuflags -sse+mmx ...
ffmpeg -cpuflags mmx ...
ffmpeg -cpuflags 0 ...
</pre></div>
<p>Possible flags for this option are:
</p><dl compact="compact">
<dt>&lsquo;<samp>x86</samp>&rsquo;</dt>
<dd><dl compact="compact">
<dt>&lsquo;<samp>mmx</samp>&rsquo;</dt>
<dt>&lsquo;<samp>mmxext</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse2</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse2slow</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse3</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse3slow</samp>&rsquo;</dt>
<dt>&lsquo;<samp>ssse3</samp>&rsquo;</dt>
<dt>&lsquo;<samp>atom</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse4.1</samp>&rsquo;</dt>
<dt>&lsquo;<samp>sse4.2</samp>&rsquo;</dt>
<dt>&lsquo;<samp>avx</samp>&rsquo;</dt>
<dt>&lsquo;<samp>avx2</samp>&rsquo;</dt>
<dt>&lsquo;<samp>xop</samp>&rsquo;</dt>
<dt>&lsquo;<samp>fma3</samp>&rsquo;</dt>
<dt>&lsquo;<samp>fma4</samp>&rsquo;</dt>
<dt>&lsquo;<samp>3dnow</samp>&rsquo;</dt>
<dt>&lsquo;<samp>3dnowext</samp>&rsquo;</dt>
<dt>&lsquo;<samp>bmi1</samp>&rsquo;</dt>
<dt>&lsquo;<samp>bmi2</samp>&rsquo;</dt>
<dt>&lsquo;<samp>cmov</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp>ARM</samp>&rsquo;</dt>
<dd><dl compact="compact">
<dt>&lsquo;<samp>armv5te</samp>&rsquo;</dt>
<dt>&lsquo;<samp>armv6</samp>&rsquo;</dt>
<dt>&lsquo;<samp>armv6t2</samp>&rsquo;</dt>
<dt>&lsquo;<samp>vfp</samp>&rsquo;</dt>
<dt>&lsquo;<samp>vfpv3</samp>&rsquo;</dt>
<dt>&lsquo;<samp>neon</samp>&rsquo;</dt>
<dt>&lsquo;<samp>setend</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp>AArch64</samp>&rsquo;</dt>
<dd><dl compact="compact">
<dt>&lsquo;<samp>armv8</samp>&rsquo;</dt>
<dt>&lsquo;<samp>vfp</samp>&rsquo;</dt>
<dt>&lsquo;<samp>neon</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp>PowerPC</samp>&rsquo;</dt>
<dd><dl compact="compact">
<dt>&lsquo;<samp>altivec</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp>Specific Processors</samp>&rsquo;</dt>
<dd><dl compact="compact">
<dt>&lsquo;<samp>pentium2</samp>&rsquo;</dt>
<dt>&lsquo;<samp>pentium3</samp>&rsquo;</dt>
<dt>&lsquo;<samp>pentium4</samp>&rsquo;</dt>
<dt>&lsquo;<samp>k6</samp>&rsquo;</dt>
<dt>&lsquo;<samp>k62</samp>&rsquo;</dt>
<dt>&lsquo;<samp>athlon</samp>&rsquo;</dt>
<dt>&lsquo;<samp>athlonxp</samp>&rsquo;</dt>
<dt>&lsquo;<samp>k8</samp>&rsquo;</dt>
</dl>
</dd>
</dl>

</dd>
<dt><samp>-opencl_bench</samp></dt>
<dd><p>This option is used to benchmark all available OpenCL devices and print the
results. This option is only available when FFmpeg has been compiled with
<code>--enable-opencl</code>.
</p>
<p>When FFmpeg is configured with <code>--enable-opencl</code>, the options for the
global OpenCL context are set via <samp>-opencl_options</samp>. See the
&quot;OpenCL Options&quot; section in the ffmpeg-utils manual for the complete list of
supported options. Amongst others, these options include the ability to select
a specific platform and device to run the OpenCL code on. By default, FFmpeg
will run on the first device of the first platform. While the options for the
global OpenCL context provide flexibility to the user in selecting the OpenCL
device of their choice, most users would probably want to select the fastest
OpenCL device for their system.
</p>
<p>This option assists the selection of the most efficient configuration by
identifying the appropriate device for the user&rsquo;s system. The built-in
benchmark is run on all the OpenCL devices and the performance is measured for
each device. The devices in the results list are sorted based on their
performance with the fastest device listed first. The user can subsequently
invoke <code>ffmpeg</code> using the device deemed most appropriate via
<samp>-opencl_options</samp> to obtain the best performance for the OpenCL
accelerated code.
</p>
<p>Typical usage to use the fastest OpenCL device involve the following steps.
</p>
<p>Run the command:
</p><div class="example">
<pre class="example">ffmpeg -opencl_bench
</pre></div>
<p>Note down the platform ID (<var>pidx</var>) and device ID (<var>didx</var>) of the first
i.e. fastest device in the list.
Select the platform and device using the command:
</p><div class="example">
<pre class="example">ffmpeg -opencl_options platform_idx=<var>pidx</var>:device_idx=<var>didx</var> ...
</pre></div>

</dd>
<dt><samp>-opencl_options options (<em>global</em>)</samp></dt>
<dd><p>Set OpenCL environment options. This option is only available when
FFmpeg has been compiled with <code>--enable-opencl</code>.
</p>
<p><var>options</var> must be a list of <var>key</var>=<var>value</var> option pairs
separated by &rsquo;:&rsquo;. See the &ldquo;OpenCL Options&rdquo; section in the
ffmpeg-utils manual for the list of supported options.
</p></dd>
</dl>

<a name="AVOptions"></a>
<h3 class="section">3.3 AVOptions<span class="pull-right"><a class="anchor hidden-xs" href="#AVOptions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-AVOptions" aria-hidden="true">TOC</a></span></h3>

<p>These options are provided directly by the libavformat, libavdevice and
libavcodec libraries. To see the list of available AVOptions, use the
<samp>-help</samp> option. They are separated into two categories:
</p><dl compact="compact">
<dt><samp>generic</samp></dt>
<dd><p>These options can be set for any container, codec or device. Generic options
are listed under AVFormatContext options for containers/devices and under
AVCodecContext options for codecs.
</p></dd>
<dt><samp>private</samp></dt>
<dd><p>These options are specific to the given container, device or codec. Private
options are listed under their corresponding containers/devices/codecs.
</p></dd>
</dl>

<p>For example to write an ID3v2.3 header instead of a default ID3v2.4 to
an MP3 file, use the <samp>id3v2_version</samp> private option of the MP3
muxer:
</p><div class="example">
<pre class="example">ffmpeg -i input.flac -id3v2_version 3 out.mp3
</pre></div>

<p>All codec AVOptions are per-stream, and thus a stream specifier
should be attached to them.
</p>
<p>Note: the <samp>-nooption</samp> syntax cannot be used for boolean
AVOptions, use <samp>-option 0</samp>/<samp>-option 1</samp>.
</p>
<p>Note: the old undocumented way of specifying per-stream AVOptions by
prepending v/a/s to the options name is now obsolete and will be
removed soon.
</p>
<a name="Main-options"></a>
<h3 class="section">3.4 Main options<span class="pull-right"><a class="anchor hidden-xs" href="#Main-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Main-options" aria-hidden="true">TOC</a></span></h3>

<dl compact="compact">
<dt><samp>-x <var>width</var></samp></dt>
<dd><p>Force displayed width.
</p></dd>
<dt><samp>-y <var>height</var></samp></dt>
<dd><p>Force displayed height.
</p></dd>
<dt><samp>-s <var>size</var></samp></dt>
<dd><p>Set frame size (WxH or abbreviation), needed for videos which do
not contain a header with the frame size like raw YUV.  This option
has been deprecated in favor of private options, try -video_size.
</p></dd>
<dt><samp>-fs</samp></dt>
<dd><p>Start in fullscreen mode.
</p></dd>
<dt><samp>-an</samp></dt>
<dd><p>Disable audio.
</p></dd>
<dt><samp>-vn</samp></dt>
<dd><p>Disable video.
</p></dd>
<dt><samp>-sn</samp></dt>
<dd><p>Disable subtitles.
</p></dd>
<dt><samp>-ss <var>pos</var></samp></dt>
<dd><p>Seek to <var>pos</var>. Note that in most formats it is not possible to seek
exactly, so <code>ffplay</code> will seek to the nearest seek point to
<var>pos</var>.
</p>
<p><var>pos</var> must be a time duration specification,
see <a href="ffmpeg-utils.html#time-duration-syntax">(ffmpeg-utils)the Time duration section in the ffmpeg-utils(1) manual</a>.
</p></dd>
<dt><samp>-t <var>duration</var></samp></dt>
<dd><p>Play <var>duration</var> seconds of audio/video.
</p>
<p><var>duration</var> must be a time duration specification,
see <a href="ffmpeg-utils.html#time-duration-syntax">(ffmpeg-utils)the Time duration section in the ffmpeg-utils(1) manual</a>.
</p></dd>
<dt><samp>-bytes</samp></dt>
<dd><p>Seek by bytes.
</p></dd>
<dt><samp>-nodisp</samp></dt>
<dd><p>Disable graphical display.
</p></dd>
<dt><samp>-f <var>fmt</var></samp></dt>
<dd><p>Force format.
</p></dd>
<dt><samp>-window_title <var>title</var></samp></dt>
<dd><p>Set window title (default is the input filename).
</p></dd>
<dt><samp>-loop <var>number</var></samp></dt>
<dd><p>Loops movie playback &lt;number&gt; times. 0 means forever.
</p></dd>
<dt><samp>-showmode <var>mode</var></samp></dt>
<dd><p>Set the show mode to use.
Available values for <var>mode</var> are:
</p><dl compact="compact">
<dt>&lsquo;<samp>0, video</samp>&rsquo;</dt>
<dd><p>show video
</p></dd>
<dt>&lsquo;<samp>1, waves</samp>&rsquo;</dt>
<dd><p>show audio waves
</p></dd>
<dt>&lsquo;<samp>2, rdft</samp>&rsquo;</dt>
<dd><p>show audio frequency band using RDFT ((Inverse) Real Discrete Fourier Transform)
</p></dd>
</dl>

<p>Default value is &quot;video&quot;, if video is not present or cannot be played
&quot;rdft&quot; is automatically selected.
</p>
<p>You can interactively cycle through the available show modes by
pressing the key <tt class="key">w</tt>.
</p>
</dd>
<dt><samp>-vf <var>filtergraph</var></samp></dt>
<dd><p>Create the filtergraph specified by <var>filtergraph</var> and use it to
filter the video stream.
</p>
<p><var>filtergraph</var> is a description of the filtergraph to apply to
the stream, and must have a single video input and a single video
output. In the filtergraph, the input is associated to the label
<code>in</code>, and the output to the label <code>out</code>. See the
ffmpeg-filters manual for more information about the filtergraph
syntax.
</p>
<p>You can specify this parameter multiple times and cycle through the specified
filtergraphs along with the show modes by pressing the key <tt class="key">w</tt>.
</p>
</dd>
<dt><samp>-af <var>filtergraph</var></samp></dt>
<dd><p><var>filtergraph</var> is a description of the filtergraph to apply to
the input audio.
Use the option &quot;-filters&quot; to show all the available filters (including
sources and sinks).
</p>
</dd>
<dt><samp>-i <var>input_file</var></samp></dt>
<dd><p>Read <var>input_file</var>.
</p></dd>
</dl>

<a name="Advanced-options"></a>
<h3 class="section">3.5 Advanced options<span class="pull-right"><a class="anchor hidden-xs" href="#Advanced-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Advanced-options" aria-hidden="true">TOC</a></span></h3>
<dl compact="compact">
<dt><samp>-pix_fmt <var>format</var></samp></dt>
<dd><p>Set pixel format.
This option has been deprecated in favor of private options, try -pixel_format.
</p>
</dd>
<dt><samp>-stats</samp></dt>
<dd><p>Print several playback statistics, in particular show the stream
duration, the codec parameters, the current position in the stream and
the audio/video synchronisation drift. It is on by default, to
explicitly disable it you need to specify <code>-nostats</code>.
</p>
</dd>
<dt><samp>-fast</samp></dt>
<dd><p>Non-spec-compliant optimizations.
</p></dd>
<dt><samp>-genpts</samp></dt>
<dd><p>Generate pts.
</p></dd>
<dt><samp>-sync <var>type</var></samp></dt>
<dd><p>Set the master clock to audio (<code>type=audio</code>), video
(<code>type=video</code>) or external (<code>type=ext</code>). Default is audio. The
master clock is used to control audio-video synchronization. Most media
players use audio as master clock, but in some cases (streaming or high
quality broadcast) it is necessary to change that. This option is mainly
used for debugging purposes.
</p></dd>
<dt><samp>-ast <var>audio_stream_specifier</var></samp></dt>
<dd><p>Select the desired audio stream using the given stream specifier. The stream
specifiers are described in the <a href="#Stream-specifiers">Stream specifiers</a> chapter. If this option
is not specified, the &quot;best&quot; audio stream is selected in the program of the
already selected video stream.
</p></dd>
<dt><samp>-vst <var>video_stream_specifier</var></samp></dt>
<dd><p>Select the desired video stream using the given stream specifier. The stream
specifiers are described in the <a href="#Stream-specifiers">Stream specifiers</a> chapter. If this option
is not specified, the &quot;best&quot; video stream is selected.
</p></dd>
<dt><samp>-sst <var>subtitle_stream_specifier</var></samp></dt>
<dd><p>Select the desired subtitle stream using the given stream specifier. The stream
specifiers are described in the <a href="#Stream-specifiers">Stream specifiers</a> chapter. If this option
is not specified, the &quot;best&quot; subtitle stream is selected in the program of the
already selected video or audio stream.
</p></dd>
<dt><samp>-autoexit</samp></dt>
<dd><p>Exit when video is done playing.
</p></dd>
<dt><samp>-exitonkeydown</samp></dt>
<dd><p>Exit if any key is pressed.
</p></dd>
<dt><samp>-exitonmousedown</samp></dt>
<dd><p>Exit if any mouse button is pressed.
</p>
</dd>
<dt><samp>-codec:<var>media_specifier</var> <var>codec_name</var></samp></dt>
<dd><p>Force a specific decoder implementation for the stream identified by
<var>media_specifier</var>, which can assume the values <code>a</code> (audio),
<code>v</code> (video), and <code>s</code> subtitle.
</p>
</dd>
<dt><samp>-acodec <var>codec_name</var></samp></dt>
<dd><p>Force a specific audio decoder.
</p>
</dd>
<dt><samp>-vcodec <var>codec_name</var></samp></dt>
<dd><p>Force a specific video decoder.
</p>
</dd>
<dt><samp>-scodec <var>codec_name</var></samp></dt>
<dd><p>Force a specific subtitle decoder.
</p>
</dd>
<dt><samp>-autorotate</samp></dt>
<dd><p>Automatically rotate the video according to file metadata. Enabled by
default, use <samp>-noautorotate</samp> to disable it.
</p>
</dd>
<dt><samp>-framedrop</samp></dt>
<dd><p>Drop video frames if video is out of sync. Enabled by default if the master
clock is not set to video. Use this option to enable frame dropping for all
master clock sources, use <samp>-noframedrop</samp> to disable it.
</p>
</dd>
<dt><samp>-infbuf</samp></dt>
<dd><p>Do not limit the input buffer size, read as much data as possible from the
input as soon as possible. Enabled by default for realtime streams, where data
may be dropped if not read in time. Use this option to enable infinite buffers
for all inputs, use <samp>-noinfbuf</samp> to disable it.
</p>
</dd>
</dl>

<a name="While-playing"></a>
<h3 class="section">3.6 While playing<span class="pull-right"><a class="anchor hidden-xs" href="#While-playing" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-While-playing" aria-hidden="true">TOC</a></span></h3>

<dl compact="compact">
<dt><tt class="key">q, ESC</tt></dt>
<dd><p>Quit.
</p>
</dd>
<dt><tt class="key">f</tt></dt>
<dd><p>Toggle full screen.
</p>
</dd>
<dt><tt class="key">p, SPC</tt></dt>
<dd><p>Pause.
</p>
</dd>
<dt><tt class="key">m</tt></dt>
<dd><p>Toggle mute.
</p>
</dd>
<dt><tt class="key">9, 0</tt></dt>
<dd><p>Decrease and increase volume respectively.
</p>
</dd>
<dt><tt class="key">/, *</tt></dt>
<dd><p>Decrease and increase volume respectively.
</p>
</dd>
<dt><tt class="key">a</tt></dt>
<dd><p>Cycle audio channel in the current program.
</p>
</dd>
<dt><tt class="key">v</tt></dt>
<dd><p>Cycle video channel.
</p>
</dd>
<dt><tt class="key">t</tt></dt>
<dd><p>Cycle subtitle channel in the current program.
</p>
</dd>
<dt><tt class="key">c</tt></dt>
<dd><p>Cycle program.
</p>
</dd>
<dt><tt class="key">w</tt></dt>
<dd><p>Cycle video filters or show modes.
</p>
</dd>
<dt><tt class="key">s</tt></dt>
<dd><p>Step to the next frame.
</p>
<p>Pause if the stream is not already paused, step to the next video
frame, and pause.
</p>
</dd>
<dt><tt class="key">left/right</tt></dt>
<dd><p>Seek backward/forward 10 seconds.
</p>
</dd>
<dt><tt class="key">down/up</tt></dt>
<dd><p>Seek backward/forward 1 minute.
</p>
</dd>
<dt><tt class="key">page down/page up</tt></dt>
<dd><p>Seek to the previous/next chapter.
or if there are no chapters
Seek backward/forward 10 minutes.
</p>
</dd>
<dt><tt class="key">right mouse click</tt></dt>
<dd><p>Seek to percentage in file corresponding to fraction of width.
</p>
</dd>
<dt><tt class="key">left mouse double-click</tt></dt>
<dd><p>Toggle full screen.
</p>
</dd>
</dl>



<a name="See-Also"></a>
<h2 class="chapter">4 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffplay-all.html">ffmpeg-all</a>,
<a href="ffmpeg.html">ffmpeg</a>, <a href="ffprobe.html">ffprobe</a>, <a href="ffserver.html">ffserver</a>,
<a href="ffmpeg-utils.html">ffmpeg-utils</a>,
<a href="ffmpeg-scaler.html">ffmpeg-scaler</a>,
<a href="ffmpeg-resampler.html">ffmpeg-resampler</a>,
<a href="ffmpeg-codecs.html">ffmpeg-codecs</a>,
<a href="ffmpeg-bitstream-filters.html">ffmpeg-bitstream-filters</a>,
<a href="ffmpeg-formats.html">ffmpeg-formats</a>,
<a href="ffmpeg-devices.html">ffmpeg-devices</a>,
<a href="ffmpeg-protocols.html">ffmpeg-protocols</a>,
<a href="ffmpeg-filters.html">ffmpeg-filters</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">5 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(git://source.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="http://source.ffmpeg.org">http://source.ffmpeg.org</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>


      <p style="font-size: small;">
        This document was generated using <a href="http://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
