// Intelligent Reconnection Manager with Exponential Backoff
// Handles automatic reconnection with adaptive strategies

use std::collections::HashMap;
use std::time::{Duration, Instant};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::error_detector::{StreamError, RecoveryAction};
use crate::ffmpeg_command_builder::{StabilityMode, determine_stability_mode};

#[derive(Debug, Clone)]
pub struct ReconnectionAttempt {
    pub attempt_number: u32,
    pub timestamp: DateTime<Utc>,
    pub error_type: StreamError,
    pub stability_mode_used: StabilityMode,
    pub success: bool,
    pub delay_seconds: u64,
}

#[derive(Debug, Clone)]
pub struct ReconnectionStrategy {
    pub max_attempts: u32,
    pub base_delay_seconds: u64,
    pub max_delay_seconds: u64,
    pub backoff_multiplier: f64,
    pub stability_degradation_threshold: u32,
}

impl Default for ReconnectionStrategy {
    fn default() -> Self {
        Self {
            max_attempts: 10,
            base_delay_seconds: 5,
            max_delay_seconds: 300, // 5 minutes max
            backoff_multiplier: 1.5,
            stability_degradation_threshold: 3,
        }
    }
}

pub struct ReconnectionManager {
    strategy: ReconnectionStrategy,
    stream_attempts: HashMap<Uuid, Vec<ReconnectionAttempt>>,
    stream_stability_modes: HashMap<Uuid, StabilityMode>,
    last_attempt_time: HashMap<Uuid, Instant>,
}

impl ReconnectionManager {
    pub fn new() -> Self {
        Self {
            strategy: ReconnectionStrategy::default(),
            stream_attempts: HashMap::new(),
            stream_stability_modes: HashMap::new(),
            last_attempt_time: HashMap::new(),
        }
    }

    pub fn with_strategy(strategy: ReconnectionStrategy) -> Self {
        Self {
            strategy,
            stream_attempts: HashMap::new(),
            stream_stability_modes: HashMap::new(),
            last_attempt_time: HashMap::new(),
        }
    }

    pub fn should_attempt_reconnection(&mut self, stream_id: Uuid, error: &StreamError) -> bool {
        // Check if error is recoverable
        if !error.is_recoverable() {
            log::warn!("🚫 Error {:?} is not recoverable for stream {}", error, stream_id);
            return false;
        }

        // Get current attempt count
        let attempts = self.stream_attempts.entry(stream_id).or_insert_with(Vec::new);
        let current_attempts = attempts.len() as u32;

        // Check max attempts
        if current_attempts >= self.strategy.max_attempts {
            log::warn!("🚫 Max reconnection attempts ({}) reached for stream {}", 
                      self.strategy.max_attempts, stream_id);
            return false;
        }

        // Check if enough time has passed since last attempt
        if let Some(last_time) = self.last_attempt_time.get(&stream_id) {
            let delay = self.calculate_delay(current_attempts);
            if last_time.elapsed() < Duration::from_secs(delay) {
                log::debug!("⏳ Not enough time passed since last reconnection attempt for stream {}", stream_id);
                return false;
            }
        }

        log::info!("✅ Reconnection attempt {} approved for stream {} (error: {:?})", 
                  current_attempts + 1, stream_id, error);
        true
    }

    pub fn record_reconnection_attempt(
        &mut self, 
        stream_id: Uuid, 
        error: StreamError, 
        stability_mode: StabilityMode,
        success: bool
    ) {
        let attempt_number = self.stream_attempts
            .get(&stream_id)
            .map(|attempts| attempts.len() as u32 + 1)
            .unwrap_or(1);

        let delay = self.calculate_delay(attempt_number - 1);

        let attempt = ReconnectionAttempt {
            attempt_number,
            timestamp: Utc::now(),
            error_type: error,
            stability_mode_used: stability_mode,
            success,
            delay_seconds: delay,
        };

        self.stream_attempts
            .entry(stream_id)
            .or_insert_with(Vec::new)
            .push(attempt);

        self.last_attempt_time.insert(stream_id, Instant::now());

        if success {
            log::info!("✅ Reconnection attempt {} succeeded for stream {}", attempt_number, stream_id);
            // Reset attempts on success
            self.stream_attempts.get_mut(&stream_id).unwrap().clear();
        } else {
            log::warn!("❌ Reconnection attempt {} failed for stream {}", attempt_number, stream_id);
        }
    }

    pub fn get_next_stability_mode(&mut self, stream_id: Uuid, current_error: &StreamError) -> StabilityMode {
        let attempts = self.stream_attempts.get(&stream_id).unwrap_or(&Vec::new());
        let failed_attempts = attempts.iter().filter(|a| !a.success).count() as u32;

        // Get last error type for context
        let last_error_type = attempts.last().map(|a| format!("{:?}", a.error_type));

        // Use the determine_stability_mode function with error history
        let suggested_mode = determine_stability_mode(failed_attempts, last_error_type.as_deref());

        // Store the mode for this stream
        self.stream_stability_modes.insert(stream_id, suggested_mode.clone());

        log::info!("🎯 Stability mode for stream {} (after {} failed attempts): {:?}", 
                  stream_id, failed_attempts, suggested_mode);

        suggested_mode
    }

    pub fn get_recovery_action(&self, error: &StreamError) -> RecoveryAction {
        error.suggested_action()
    }

    pub fn calculate_delay(&self, attempt_number: u32) -> u64 {
        if attempt_number == 0 {
            return self.strategy.base_delay_seconds;
        }

        let delay = (self.strategy.base_delay_seconds as f64) * 
                   self.strategy.backoff_multiplier.powi(attempt_number as i32);
        
        std::cmp::min(delay as u64, self.strategy.max_delay_seconds)
    }

    pub fn get_reconnection_stats(&self, stream_id: Uuid) -> Option<ReconnectionStats> {
        let attempts = self.stream_attempts.get(&stream_id)?;
        
        if attempts.is_empty() {
            return None;
        }

        let total_attempts = attempts.len();
        let successful_attempts = attempts.iter().filter(|a| a.success).count();
        let failed_attempts = total_attempts - successful_attempts;
        
        let last_attempt = attempts.last().unwrap();
        let first_attempt = attempts.first().unwrap();
        
        let total_duration = last_attempt.timestamp - first_attempt.timestamp;
        
        Some(ReconnectionStats {
            total_attempts: total_attempts as u32,
            successful_attempts: successful_attempts as u32,
            failed_attempts: failed_attempts as u32,
            success_rate: (successful_attempts as f64 / total_attempts as f64) * 100.0,
            total_duration_minutes: total_duration.num_minutes(),
            last_attempt_time: last_attempt.timestamp,
            current_stability_mode: self.stream_stability_modes.get(&stream_id).cloned(),
        })
    }

    pub fn cleanup_old_attempts(&mut self, max_age_hours: i64) {
        let cutoff = Utc::now() - chrono::Duration::hours(max_age_hours);
        
        for attempts in self.stream_attempts.values_mut() {
            attempts.retain(|attempt| attempt.timestamp > cutoff);
        }
        
        // Remove empty entries
        self.stream_attempts.retain(|_, attempts| !attempts.is_empty());
    }

    pub fn reset_stream_attempts(&mut self, stream_id: Uuid) {
        self.stream_attempts.remove(&stream_id);
        self.stream_stability_modes.remove(&stream_id);
        self.last_attempt_time.remove(&stream_id);
        log::info!("🔄 Reset reconnection history for stream {}", stream_id);
    }

    pub fn get_all_stream_stats(&self) -> HashMap<Uuid, ReconnectionStats> {
        let mut stats = HashMap::new();
        
        for stream_id in self.stream_attempts.keys() {
            if let Some(stream_stats) = self.get_reconnection_stats(*stream_id) {
                stats.insert(*stream_id, stream_stats);
            }
        }
        
        stats
    }
}

#[derive(Debug, Clone)]
pub struct ReconnectionStats {
    pub total_attempts: u32,
    pub successful_attempts: u32,
    pub failed_attempts: u32,
    pub success_rate: f64,
    pub total_duration_minutes: i64,
    pub last_attempt_time: DateTime<Utc>,
    pub current_stability_mode: Option<StabilityMode>,
}

impl Default for ReconnectionManager {
    fn default() -> Self {
        Self::new()
    }
}

// Helper function to create different strategies for different scenarios
impl ReconnectionStrategy {
    pub fn aggressive() -> Self {
        Self {
            max_attempts: 15,
            base_delay_seconds: 2,
            max_delay_seconds: 120,
            backoff_multiplier: 1.3,
            stability_degradation_threshold: 2,
        }
    }

    pub fn conservative() -> Self {
        Self {
            max_attempts: 5,
            base_delay_seconds: 10,
            max_delay_seconds: 600,
            backoff_multiplier: 2.0,
            stability_degradation_threshold: 5,
        }
    }

    pub fn production() -> Self {
        Self {
            max_attempts: 8,
            base_delay_seconds: 5,
            max_delay_seconds: 300,
            backoff_multiplier: 1.5,
            stability_degradation_threshold: 3,
        }
    }
}
