// Unified FFmpeg Command Builder
// Thay thế tất cả các modules FFmpeg khác để tránh conflicts và inconsistency

use std::process::{Command, Stdio};
use std::path::PathBuf;
use anyhow::Result;
use crate::models::AppSettings;

#[derive(Debu<PERSON>, <PERSON>lone, PartialEq, Eq, Hash)]
pub enum StabilityMode {
    HighQuality,    // Use user settings, best quality
    Stable,         // Conservative settings for reliability
    Emergency,      // Minimal settings for maximum stability
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct StreamingProfile {
    pub bitrate: u32,
    pub max_bitrate: u32,
    pub buffer_size: u32,
    pub preset: String,
    pub profile: String,
    pub gop_size: u32,
    pub audio_bitrate: u32,
}

impl StreamingProfile {
    pub fn from_stability_mode(mode: StabilityMode, user_bitrate: u32) -> Self {
        match mode {
            StabilityMode::HighQuality => Self {
                bitrate: std::cmp::min(user_bitrate, 6000), // Cap at 6000k for safety
                max_bitrate: std::cmp::min(user_bitrate * 12 / 10, 7200), // 120% of target
                buffer_size: std::cmp::min(user_bitrate * 2, 12000), // 2x buffer
                preset: "fast".to_string(),
                profile: "high".to_string(),
                gop_size: 60, // 2 seconds at 30fps
                audio_bitrate: 128,
            },
            StabilityMode::Stable => Self {
                bitrate: std::cmp::min(user_bitrate, 3000), // Cap at 3000k
                max_bitrate: std::cmp::min(user_bitrate, 3000), // Same as target
                buffer_size: std::cmp::min(user_bitrate * 2, 6000), // 2x buffer
                preset: "ultrafast".to_string(),
                profile: "baseline".to_string(),
                gop_size: 30, // 1 second at 30fps
                audio_bitrate: 96,
            },
            StabilityMode::Emergency => Self {
                bitrate: 1000, // Fixed 1000k for maximum stability
                max_bitrate: 1000,
                buffer_size: 1000,
                preset: "ultrafast".to_string(),
                profile: "baseline".to_string(),
                gop_size: 30,
                audio_bitrate: 64,
            },
        }
    }
}

pub struct FFmpegCommandBuilder {
    ffmpeg_path: PathBuf,
    profile: StreamingProfile,
    stability_mode: StabilityMode,
}

impl FFmpegCommandBuilder {
    pub fn new(ffmpeg_path: PathBuf, settings: &AppSettings, stability_mode: StabilityMode) -> Self {
        let user_bitrate = settings.default_bitrate.parse::<u32>().unwrap_or(3000);
        let profile = StreamingProfile::from_stability_mode(stability_mode.clone(), user_bitrate);
        
        log::info!("🎯 FFmpeg Builder - Mode: {:?}, Bitrate: {}k, Profile: {}", 
                  stability_mode, profile.bitrate, profile.preset);
        
        Self {
            ffmpeg_path,
            profile,
            stability_mode,
        }
    }

    pub fn build_streaming_command(&self, input_file: &str, rtmp_url: &str) -> Result<Command> {
        let mut cmd = Command::new(&self.ffmpeg_path);
        
        // INPUT CONFIGURATION
        cmd.arg("-re")                              // Read at native frame rate
            .arg("-stream_loop").arg("-1")         // Loop video infinitely
            .arg("-i").arg(input_file);             // Input file
        
        // INPUT STABILITY (always include for robustness)
        cmd.arg("-avoid_negative_ts").arg("make_zero")  // Handle negative timestamps
            .arg("-fflags").arg("+genpts+igndts")       // Generate PTS, ignore DTS issues
            .arg("-use_wallclock_as_timestamps").arg("1"); // Use system time
        
        // VIDEO ENCODING
        cmd.arg("-c:v").arg("libx264")              // H.264 codec
            .arg("-preset").arg(&self.profile.preset)
            .arg("-profile:v").arg(&self.profile.profile)
            .arg("-level").arg("4.1")               // Compatible level
            .arg("-b:v").arg(format!("{}k", self.profile.bitrate))
            .arg("-maxrate").arg(format!("{}k", self.profile.max_bitrate))
            .arg("-bufsize").arg(format!("{}k", self.profile.buffer_size));
        
        // GOP STRUCTURE
        cmd.arg("-g").arg(self.profile.gop_size.to_string())
            .arg("-keyint_min").arg((self.profile.gop_size / 2).to_string())
            .arg("-sc_threshold").arg("0")          // Disable scene change detection
            .arg("-pix_fmt").arg("yuv420p");        // Compatible pixel format
        
        // Add CBR for stable modes
        if self.stability_mode != StabilityMode::HighQuality {
            cmd.arg("-x264-params").arg("nal-hrd=cbr:force-cfr=1");
        }
        
        // AUDIO ENCODING
        cmd.arg("-c:a").arg("aac")                  // Audio codec
            .arg("-b:a").arg(format!("{}k", self.profile.audio_bitrate))
            .arg("-ar").arg("44100")                // Audio sample rate
            .arg("-ac").arg("2")                    // Stereo channels
            .arg("-strict").arg("-2");              // Allow experimental AAC
        
        self.add_network_resilience(&mut cmd, rtmp_url);
        self.add_output_settings(&mut cmd);
        
        Ok(cmd)
    }

    fn add_network_resilience(&self, cmd: &mut Command, rtmp_url: &str) {
        // FLV OUTPUT with appropriate flags
        cmd.arg("-f").arg("flv");
        
        match self.stability_mode {
            StabilityMode::HighQuality => {
                cmd.arg("-flvflags").arg("no_duration_filesize+no_metadata");
            },
            _ => {
                cmd.arg("-flvflags").arg("no_duration_filesize+no_metadata+no_sequence_end");
            }
        }
        
        // RTMP SETTINGS
        cmd.arg("-rtmp_live").arg("live")           // Live streaming mode
            .arg("-rtmp_buffer").arg("5000")        // 5 second buffer
            .arg("-rtmp_flush_interval").arg("100"); // Flush every 100ms
        
        // RECONNECTION (only for stable/emergency modes)
        if self.stability_mode != StabilityMode::HighQuality {
            cmd.arg("-reconnect").arg("1")
                .arg("-reconnect_at_eof").arg("1")
                .arg("-reconnect_streamed").arg("1")
                .arg("-reconnect_delay_max").arg("30")
                .arg("-reconnect_on_network_error").arg("1")
                .arg("-reconnect_on_http_error").arg("1");
        }
        
        // MEMORY MANAGEMENT
        let queue_size = match self.stability_mode {
            StabilityMode::HighQuality => "4096",
            StabilityMode::Stable => "2048", 
            StabilityMode::Emergency => "1024",
        };
        
        cmd.arg("-max_muxing_queue_size").arg(queue_size)
            .arg("-muxdelay").arg("0")
            .arg("-muxpreload").arg("0");
        
        // TIMEOUT SETTINGS
        cmd.arg("-timeout").arg("30000000")         // 30 second timeout
            .arg("-max_delay").arg("5000000")       // 5 second max delay
            .arg("-user_agent").arg("FFmpeg/4.4.0");
        
        // RTMP URL
        cmd.arg(rtmp_url);
    }

    fn add_output_settings(&self, cmd: &mut Command) {
        // LOG LEVEL - Always use "info" to catch connection status
        cmd.arg("-loglevel").arg("info")            // Need info level for debugging
            .arg("-stats")                          // Show encoding stats
            .arg("-nostdin")                        // No stdin interaction
            .arg("-y")                              // Overwrite without asking
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());
        
        // Hide console window on Windows
        #[cfg(target_os = "windows")]
        {
            use std::os::windows::process::CommandExt;
            cmd.creation_flags(0x08000000); // CREATE_NO_WINDOW
        }
    }
    
    pub fn get_profile(&self) -> &StreamingProfile {
        &self.profile
    }
    
    pub fn get_stability_mode(&self) -> &StabilityMode {
        &self.stability_mode
    }
}

// Helper function to determine appropriate stability mode based on error history
pub fn determine_stability_mode(error_count: u32, last_error_type: Option<&str>) -> StabilityMode {
    match (error_count, last_error_type) {
        (0..=1, _) => StabilityMode::HighQuality,
        (2..=3, Some(err)) if err.contains("10054") || err.contains("10053") => StabilityMode::Stable,
        (4..=5, _) => StabilityMode::Stable,
        _ => StabilityMode::Emergency,
    }
}
