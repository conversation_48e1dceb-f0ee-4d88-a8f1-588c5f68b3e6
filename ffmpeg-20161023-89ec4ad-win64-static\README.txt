This is a FFmpeg win64 static build by <PERSON>.

Zeranoe's FFmpeg Builds Home Page: <http://ffmpeg.zeranoe.com/builds/>

FFmpeg version: 20161023-89ec4ad

This FFmpeg build was configured with:
  --enable-gpl
  --enable-version3
  --disable-w32threads
  --enable-dxva2
  --enable-libmfx
  --enable-nvenc
  --enable-avisynth
  --enable-bzlib
  --enable-libebur128
  --enable-fontconfig
  --enable-frei0r
  --enable-gnutls
  --enable-iconv
  --enable-libass
  --enable-libbluray
  --enable-libbs2b
  --enable-libcaca
  --enable-libfreetype
  --enable-libgme
  --enable-libgsm
  --enable-libilbc
  --enable-libmodplug
  --enable-libmp3lame
  --enable-libopencore-amrnb
  --enable-libopencore-amrwb
  --enable-libopenh264
  --enable-libopenjpeg
  --enable-libopus
  --enable-librtmp
  --enable-libschroedinger
  --enable-libsnappy
  --enable-libsoxr
  --enable-libspeex
  --enable-libtheora
  --enable-libtwolame
  --enable-libvidstab
  --enable-libvo-amrwbenc
  --enable-libvorbis
  --enable-libvpx
  --enable-libwavpack
  --enable-libwebp
  --enable-libx264
  --enable-libx265
  --enable-libxavs
  --enable-libxvid
  --enable-libzimg
  --enable-lzma
  --enable-decklink
  --enable-zlib

This build was compiled with the following external libraries:
  libmfx 1.19 <https://ffmpeg.zeranoe.com>
  bzip2 1.0.6 <http://bzip.org/>
  libebur128 1.1.0 <https://github.com/jiixyj/libebur128>
  Fontconfig 2.12.1 <http://freedesktop.org/wiki/Software/fontconfig>
  Frei0r 20130909-git-10d8360 <http://frei0r.dyne.org/>
  GnuTLS 3.4.15 <http://gnutls.org/>
  libiconv 1.14 <http://gnu.org/software/libiconv/>
  libass 0.13.4 <https://github.com/libass/libass>
  libbluray 0.9.3 <http://videolan.org/developers/libbluray.html>
  libbs2b 3.1.0 <http://bs2b.sourceforge.net/>
  libcaca 0.99.beta18 <http://caca.zoy.org/wiki/libcaca>
  FreeType 2.7 <http://freetype.sourceforge.net/>
  Game Music Emu 0.6.0 <https://bitbucket.org/mpyne/game-music-emu/wiki/Home>
  GSM 1.0.13-4 <http://packages.debian.org/source/squeeze/libgsm>
  iLBC 20141214-git-ef04ebe <https://github.com/dekkers/libilbc/>
  Modplug-XMMS ******* <http://modplug-xmms.sourceforge.net/>
  LAME 3.99.5 <http://lame.sourceforge.net/>
  OpenCORE AMR 0.1.3 <http://sourceforge.net/projects/opencore-amr/>
  OpenH264 1.6.0 <https://github.com/cisco/openh264>
  OpenJPEG 1.5.2 <https://github.com/uclouvain/openjpeg>
  Opus 1.1.3 <http://opus-codec.org/>
  RTMPDump 20151223-git-fa8646d <http://rtmpdump.mplayerhq.hu/>
  Schroedinger 1.0.11-2.1build1 <http://diracvideo.org/>
  Snappy 20160523-32d6d7d <https://github.com/google/snappy>
  libsoxr 0.1.2 <http://sourceforge.net/projects/soxr/>
  Speex 1.2rc2 <http://speex.org/>
  Theora 1.1.1 <http://theora.org/>
  TwoLAME 0.3.13 <http://twolame.org/>
  vid.stab 0.98 <http://public.hronopik.de/vid.stab/>
  VisualOn AMR-WB 0.1.2 <https://github.com/mstorsjo/vo-amrwbenc>
  Vorbis 1.3.5 <http://vorbis.com/>
  vpx 1.6.0 <http://webmproject.org/>
  WavPack 4.80.0 <http://wavpack.com/>
  WebP 0.5.1 <https://developers.google.com/speed/webp/>
  x264 20160920-72d53ab <http://videolan.org/developers/x264.html>
  x265 2.1 <https://bitbucket.org/multicoreware/x265/wiki/Home>
  XAVS svn-r55 <http://xavs.sourceforge.net/>
  Xvid 1.3.4 <http://xvid.org/>
  z.lib 2.2.1 <https://github.com/sekrit-twc/zimg>
  XZ Utils 5.2.2 <http://tukaani.org/xz>
  zlib 1.2.8 <http://zlib.net/>

The source code for this FFmpeg build can be found at: <http://ffmpeg.zeranoe.com/builds/source/>

This build was compiled on Debian 8.6 (64-bit): <http://www.debian.org/>

GCC 5.4.0 was used to compile this FFmpeg build: <http://gcc.gnu.org/>

This build was compiled using the MinGW-w64 toolchain: <http://mingw-w64.sourceforge.net/>

Licenses for each library can be found in the 'licenses' folder.
